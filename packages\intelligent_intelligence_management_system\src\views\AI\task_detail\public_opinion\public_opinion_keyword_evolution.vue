<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #fee2e2;
          line-height: 42px;
          font-weight: bold;
          color: #991b1b;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-cpu"></i
          ><span style="margin-left: 10px">关键词演变</span>
        </div>
        <div style="display: flex">
          <div @click="saveDivAsImage" style="cursor: pointer">下载</div>
        </div>
      </div>
      <div>
        <div
          class="changeBox"
          ref="toBeCaptured"
          style="
            display: flex;
            font-size: 18px;
            justify-content: space-around;
            align-items: center;
            padding: 0 10px;
          "
        >
          <div v-for="(itemVal, indexKey) in keyword" :key="indexKey">
            <div
              class="keysWords"
              v-for="(itemsVal, indexsKey) in itemVal"
              :key="indexsKey"
              style="cursor: pointer; margin-top: 3px; font-size: 16px"
            >
              {{ indexsKey }}
            </div>
            <div
              class="keyDate"
              style="margin-top: 3px; font-size: 12px; font-weight: bold"
            >
              {{ indexKey }}
            </div>
          </div>
        </div>
        <div
          v-if="myDataObj"
          style="padding: 10px; color: #7d7878; background-color: #eee"
        >
          {{ myDataObj }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import html2canvas from "html2canvas";

export default {
  data() {
    return { keyword: null, myDataObj: null };
  },
  props: {
    activeName: {
      type: String,
      required: true,
    },
  },
  computed: {
    ...mapState({
      dataObj: function (state) {
        if (this.activeName) {
          return state.aiTaskQueue.taskDetail[this.activeName];
        }
      }, //(state) => state.aiTaskQueue.taskDetail.public_opinion,
    }),
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal) {
          for (let i = 0; i < newVal.reports.length; i++) {
            for (let str in newVal.reports[i]) {
              if (str === "bar_chart_data") {
                this.myDataObj = newVal.reports[i][str];
              }
            }
          }
          this.keyword = newVal.bar_chart_data;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    saveDivAsImage() {
      const captureElement = this.$refs.toBeCaptured;
      html2canvas(captureElement, {
        useCORS: true,
        backgroundColor: "#ffffff",
      }).then((canvas) => {
        const dataUrl = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = "关键词演变.png";
        link.click();
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
