import { timestamp } from "@/i18n/zh/list";

export default {
  namespaced: true,
  state: {
		batchTree: [],
		gotBatchTree: false,
		getTreeTimeout: null,
		batchIdList: [],
		listTruefrom: 0,
		docIndex: '',
		docList: [],
		docFrom: 0,
		docTotal: 0,
		gotDocList: false,
		keyPersonList: [],
		gotKeyPerson: false,
		mailList: [],
		gotMailList: false,
		selectMail: [],
		docTimeRange: [],
  },
  mutations: {
    clearTree (state){
			console.log("<clearTree>");

			state.gotBatchTree = false
			state.batchTree = [
        {
          id: "0",
          label: "/",
          icon: "el-icon-folder",
          children: [],
        },
      ];
		},

		setTree (state, v){
			console.log("<setTree> v:", v);

			function setNode (nodes, fatherId){
				for (let node of nodes){
					if (node.id == fatherId){
						v.data.forEach((item)=>{
							node.children.push({
								id: item.columnValues.d._._,
								label: item.columnValues.d.name.name,
								row: item.row,
								icon:
									item.columnValues.d.type.type === "dir"
										? "el-icon-folder"
										: "el-icon-tickets",
								children: [],
							})
						})
						return
					}

					if (node.children.length > 0){
						setNode(node.children, fatherId)
					}
				}
			}

			if (!v.fatherId){
				console.log("<setTree> first");
				
				v.data.forEach((item)=>{
					state.batchTree[0].children.push({
						id: item.columnValues.d._._,
						label: item.columnValues.d.name.name,
						row: item.row,
						icon:
							item.columnValues.d.type.type === "dir"
								? "el-icon-folder"
								: "el-icon-tickets",
						children: [],
					})
				})
			}else {
				console.log("<setTree> not first");

				setNode(state.batchTree, v.fatherId)
			}
		},

		clearTime(state){
			console.log("<clearTime>");

			clearTimeout(state.getTreeTimeout)
			state.getTreeTimeout = null
		},

		setTime (state){
			console.log("<setTime>");

			state.getTreeTimeout = setTimeout(()=>{
				state.gotBatchTree = true
			}, 1000)
		},

		clearListTrue(state){
			console.log("<clearListTrue>");

			state.docIndex = ""
		},

		setListTrue (state, data){
			console.log("<setListTrue> data:", data);
			
			if (data.length){
				for (let i = 0 ; i < data.length; i++){
					state.docIndex += data[i].data_range_index_name
					if (i < data.length - 1){
						state.docIndex += ','
					}
				}

				console.log("<setListTrue> docIndex:", state.docIndex);
				window.main.$store.dispatch("search/document/getDocList");
			}else {
				state.docList = []
				state.gotDocList = true
			}
		},

		clearDocFrom (state){
			console.log("<clearDocFrom>");

			state.docFrom = 0
		},

		setDocFrom (state, data){
			console.log("<setDocFrom> data:", data);

			state.docFrom = data
		},

		clearBatchIdList(state){
			console.log("<clearBatchIdList>");

			state.batchIdList = []
		},

		setBatchId(state, data){
			console.log("<setBatchId> data:", data);

			state.batchIdList = data
		},

		clearDoc(state){
			console.log("<clearDoc>");

			state.gotDocList = false
			state.docList = []
			state.docTotal = 0
		},

		setDoc (state, data){
			console.log("<setDoc> data:", data);

			if (data?.hits?.hits?.length){
				if (!state.docTotal){
					state.docTotal = data.hits.total.value
				}
				// state.docList = state.docList.concat(data.hits.hits)
				state.docList = data.hits.hits
				state.gotDocList = true
			}else {
				state.docList = []
				state.gotDocList = true
			}

			console.log("<setDoc> docTotal:", state.docTotal);
			console.log("<setDoc> docList:", state.docList);
		},

		clearPerson (state){
			console.log("<clearPerson>");

			state.gotKeyPerson = false
			state.keyPersonList = []
		},

		setPerson (state, data){
			console.log("<setPerson> data:", data);

			if (data.length){
				data.forEach(item => {
					item._source.params.forEach(param => {
						if (param.k == "email" && (param.v.length > 0)){
							param.v.forEach(mail => {
								if (mail){
									state.keyPersonList.push({
										id: item._id,
										name: item._source.name[0],
										mail: mail
									})
								}
							})
						}
					})
				});

				state.gotKeyPerson = true
				console.log("<setPerson> keyPersonList:", state.keyPersonList);

				window.main.$message.closeAll()
				window.main.$message.success("目标人列表获取成功");
			}else {
				window.main.$message.closeAll()
				window.main.$message.info("目标人列表为空");

				state.keyPersonList = []
				state.gotKeyPerson = true
			}
		},

		clearMail (state){
			console.log("<clearMail>");

			state.gotMailList = false
			state.mailList = []
		},

		setMail (state, data){
			console.log("<setMail> data:", data);

			if (data.length){
				data.forEach(item => {
					state.mailList.push({
						id: item._id,
						mail: item._source.mail,
					})
				})

				state.gotMailList = true
				console.log("<setMail> mailList:", state.mailList);
				window.main.$forceUpdate()

				window.main.$message.closeAll()
				window.main.$message.success("邮箱列表获取成功");
			}else {
				window.main.$message.closeAll()
				window.main.$message.info("邮箱列表为空");

				state.mailList = []
				state.gotMailList = true
			}
		},

		clearSelectMail (state){
			console.log("<clearMail>");

			state.selectMail = []
		},

		setSelectMail (state, data){
			console.log("<setSelectMail> data:", data); 

			state.selectMail = data
		},

		clearTimeRange (state){
			console.log("<clearTimeRange>");

			state.docTimeRange = []
		},

		setTimeRange (state, data){
			console.log("<setTimeRange> data:", data); 

			state.docTimeRange = data
		}
  },
  actions: {
		// 获取文档批次
    getBatchTree({state, dispatch, commit}, v) {
			console.log("<getBatchTree> v:", v);

			if (state.getTreeTimeout){
				commit('clearTime')
			}

			window.main.$main_socket.sendData(
				"Api.Search.SearchPrefixTable.Query",
				[
					{
						head: {
							row_key: v.row,
							size: 10,
						},
						msg: {
							table: "docs_batch",
							prefix: "",
							type: "public",
							relation: v.fatherId?v.fatherId+';'+v.type:""
						},
					},
				],
				(res) => {
					console.log("<getBatchTree> res:", res);
					if (res.length == 10){
						// 该层数据没有获取完
						console.log("<getBatchTree> not get all");

						// 将数据插入树节点
						commit('setTree', {
							data: res,
							fatherId: v.fatherId
						})

						// 继续获取数据
						dispatch('getBatchTree',{
							row: [res[res.length - 1].row],
							fatherId: v.fatherId,
							type: v.type
						})

						// 获取下一层的数据
						res.forEach((item)=>{
							if (item.columnValues.d.type.type === "dir"){
								// 获取目录
								dispatch('getBatchTree',{
									row: [],
									fatherId: item.columnValues.d._._,
									type: 'dir'
								})
								// 获取文件
								dispatch('getBatchTree',{
									row: [],
									fatherId: item.columnValues.d._._,
									type: 'file'
								})
							}
						})
					}else if(res.length > 0){
						// 该层数据已获取完
						console.log("<getBatchTree> get all");

						// 将数据插入树节点
						commit('setTree', {
							data: res,
							fatherId: v.fatherId
						})

						// 获取下一层的数据
						res.forEach((item)=>{
							if (item.columnValues.d.type.type === "dir"){
								// 获取目录
								dispatch('getBatchTree',{
									row: [],
									fatherId: item.columnValues.d._._,
									type: 'dir'
								})
								// 获取文件
								dispatch('getBatchTree',{
									row: [],
									fatherId: item.columnValues.d._._,
									type: 'file'
								})
							}
						})
					}else if (res.length == 0){
						// 该层没有数据
						console.log("<getBatchTree> get 0");

						commit ('setTime')
					}
				}
			);
    },

		// 获取文档index
		getListTrue ({state, dispatch, commit}, v){
			console.log("<getListTrue> v:", v);

			commit('setDocFrom')

			window.main.$main_socket.sendData(
        "Api.Search.DataRange.ListTrue",
        [
					{
						head: {
							from: state.listTruefrom,
							size: 10000,
						},
						control: {
							query_type: "public"
						},
						msg:{
							data_range_index_prefix: "document_file_prefix",
						}
					}
				],
        (res) => {
					console.log("<getListTrue> res:", res);
					if (res && res.length){
						commit ('setListTrue', res)
					}else {
						commit ('setListTrue', [])
					}
					
				}
      );
		},

		// 获取文档列表
		getDocList({state, dispatch, commit}, v){
			console.log("<getDocList> v:", v);

			let condition = window.main.$store.state.search.conditions.conditionsData;
			console.log("<getDocList> condition:", condition);

			console.log("<getDocList> selectMail:", state.selectMail);
			console.log("<getDocList> batch_id:", state.batchIdList);
			
			let bool = {
				bool:{
					must:[]
				}
			}

			// 构造关键字的查询语句
			if (condition.queryString){
				let searchBool = {
					[condition.ppqueryMode]: {
						"content": condition.queryString
					}
				}
				bool.bool.must.push(searchBool)
			}

			// 构造batch id查询语句
			if (state.batchIdList.length){
				let batchBool = {
					bool: {
						should: []
					}
				}
				state.batchIdList.forEach(id=>{
					batchBool.bool.should.push({
						term:{
							batch_id: id
						}
					})
				})
				bool.bool.must.push(batchBool)
			}

			// 构造type查询语句
			let typeBool = {
				bool:{
					"should":[
						{
							"term": {
								"type": "content_file"
							},
						},
						{
							"term": {
								"type": "message/rfc822"
							},
						}
					],
				}
			}
			bool.bool.must.push(typeBool)

			// 构造邮箱查询语句
			if (state.selectMail.length){
				let mailBool = {
					bool: {
						should: []
					}
				}
				state.selectMail.forEach(mail=>{
					mailBool.bool.should.push({
						"wildcard": {
							"email_from": '*'+ mail + '*'
						}
					})
				})
				bool.bool.must.push(mailBool)
			}

			// 构造时间查询语句
			if (state.docTimeRange?.length){
				let timeBool = {
					range: {
						timestamp:{
							gte: state.docTimeRange[0],
							lte: state.docTimeRange[1],
						}
					}
				}

				bool.bool.must.push(timeBool)
			}

			console.log("<getDocList> bool:", bool);

			window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
					{
						head:{
							from: state.docFrom,
							size: 20
						},
						control:{
							query_string: "",
							query_type: "public",
							condition: {
								query_mode: "match",
								time_range: '无',
								time_range_begin: 0,
								time_range_end: 0,
								collection_time_range: "无",
								collection_time_range_begin: 0,
								collection_time_range_end: 0,
							},
							add_es_query_conditions:bool,
						},
						msg: {
							data_range_index_name: state.docIndex
						}
					}
				],
        (res) => {
					console.log("<getDocList> res:", res);

					// if (res?.hits?.hits?.length){
					// 	commit('setDoc', res.hits.hits)
					// }else{
					// 	commit('setDoc', [])
					// }
					commit('setDoc', res)
				}
      );
		},

		// 获取重点人列表
		getKeyPerson ({state, dispatch, commit}, v){
			console.log("<getKeyPerson> v:", v);

			window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 10000,
            },
            control: {
              query_type: "public",
              query_string: "",
              add_es_query_conditions: {
								bool: {
									should: [
										{
											match: {
												"type": "secondary_key_person",
											},
										},
										{
											match: {
												"type": "key_person",
											},
										},
									],
								},
							},
            },
            msg: {
              data_range_index_name: "key_person",
            },
          },
        ],
        (res)=>{
					console.log("<getKeyPerson> res:", res);

					if (res?.hits?.hits?.length){
						commit('setPerson', res.hits.hits)
					}else{
						commit('serPerson', [])
					}
				}
      );
		},

		// 创建邮箱列表Oss
		createMailOss({state, dispatch, commit}, v){
			console.log("<createMailOss> v:", v);

			window.main.$main_socket.sendData(
				"Api.Search.SearchList.CreateOss",
				[
					{
						head:{},
						control:{
							query_type: "username",
							index: "email_batch_management",
							type: "_doc",
							id: "_",
							field_template: {
								"mail": {
									type: "keyword",
									index: true
								},
								"create_time": {
									type: "keyword",
									index: true
								},
							}
						},
					}
				], 
				(res) => {
					console.log("<createMailOss> res:", res);
				}
			)
		},

		// 删除邮箱列表Oss
		dropMailOss({state, dispatch, commit}, v){
			console.log("<dropMailOss> v:", v);

			window.main.$main_socket.sendData(
				"Api.Search.SearchList.DropOss",
				[
					{
						head:{},
						control:{
							query_type: "username",
							index: "email_batch_management",
							type: "_",
							id: "_",
						},
					}
				], 
				(res) => {
					console.log("<dropMailOss> res:", res);
				}
			)
		},

		// 添加邮箱
		addMail({state, dispatch, commit}, v){
			console.log("<addMail> v:", v);

			window.main.$main_socket.sendData(
				"Api.Search.SearchList.AddOss",
				[
          {
            head: {},
            control: {
              query_type: "username",
              index: "email_batch_management",
              type: "_doc",
              id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            },
            msg: {
              mail: v.mail,
							create_time: Date.now(),
							type: "mail"
            },
          },
        ],
				(res) => {
					console.log("<addMail> res:", res);

					if (res?.status === "ok"){
						window.main.$message.closeAll()
						window.main.$message.success("邮箱添加成功");

						commit('clearMail')
						dispatch('getMailList')						
					}else {
						window.main.$message.closeAll()
						window.main.$message.error("邮箱添加失败!");
					}
				}
			)
		},

		// 删除邮箱
		delMail({state, dispatch, commit}, v){
			console.log("<delMail> v:", v);

			window.main.$main_socket.sendData(
				"Api.Search.SearchList.DelOss",
				[
          {
            head: {},
            control: {
              query_type: "username",
              index: "email_batch_management",
              type: "_doc",
              id: v.id,
            }
          },
        ],
				(res) => {
					console.log("<delMail> res:", res);

					if (res?.status === "ok"){
						window.main.$message.closeAll()
						window.main.$message.success("邮箱删除成功");

						commit('clearMail')
						dispatch('getMailList')						
					}else {
						window.main.$message.closeAll()
						window.main.$message.error("邮箱删除失败!");
					}
				}
			)
		},

		// 查询邮箱列表
		getMailList ({state, dispatch, commit}, v){
			console.log("<getMailList> v:", v);

			window.main.$main_socket.sendData(
				"Api.Search.SearchList.Query",
				[
					{
						head:{
							from: 0,
							size: 10000
						},
						control:{
							query_string: "",
							query_type: "username",
							condition: {
								query_mode: "match",
								time_range: '无',
								time_range_begin: 0,
								time_range_end: 0,
								collection_time_range: "无",
								collection_time_range_begin: 0,
								collection_time_range_end: 0,
							},
							sort:[
									{'create_time':"desc"}
								],
						},
						msg: {
							data_range_index_name: "email_batch_management"
						}
					}
				],
				(res) => {
					console.log("<getMailList> res:", res);

					if (res?.hits?.hits?.length){
						commit('setMail', res.hits.hits)
					}else{
						commit('setMail', [])
					}
				}
			)
		},
  },
};
