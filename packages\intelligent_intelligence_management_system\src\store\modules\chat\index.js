import name from "@/plugins/name";

export default {
  namespaced: true,
  state: {
    sessionRecordData: [],
    hiddenStatusSessionList: false,
    toolsList: null,
    sessionList: [],
    lastRowKey: [],
    thisSessionData: {},
    sessionId: "",
    sessionListGroups: {
      今天: [],
      七天内: [],
      "30天内": [],
      "60天内": [],
      更长时间: [],
    },
    sendTools: [],
    showTools: [],
    IndicesStatus: [
      {
        num: 0,
        name: "public_sentiment_prefix",
        src: require("@/assets/images/dataImport/yuqing.png"),
      },
      {
        name: "group_content_data_prefix_telegram",
        num: 0,

        src: require("@/assets/images/dataImport/telegram.png"),
      },
      {
        name: "social_platform_timeline_prefix_facebook",
        num: 0,

        src: require("@/assets/images/dataImport/facebook.png"),
      },
      {
        name: "social_platform_timeline_prefix_linkedin",
        num: 0,

        src: require("@/assets/images/dataImport/Linkedin.png"),
      },
      {
        name: "social_platform_timeline_prefix_twitter",
        num: 0,

        src: require("@/assets/images/dataImport/twitter.png"),
      },
      {
        name: "social_work_library",
        num: 0,
        src: require("@/assets/images/dataImport/社工库.png"),
      },
    ],
    allDataNum: 0,
    mcpCall: null,
  },
  mutations: {
    //储存历史记录

    setSessionRecordData(state, v) {
      state.sessionRecordData = v;
    },
    //初始化allDataNum
    setAllDataNum(state, v) {
      state.allDataNum = v;
    },
    //设置数据总量统计
    setIndicesStatus(state, data) {
      let myData = data.public_template_status;
      function myIsNaN(value) {
        return typeof value === "number" && !isNaN(value);
      }

      // 数值单位转换函数
      function formatNumber(num) {
        if (num >= 10000000) {
          // 超过一千万，转换为亿
          return (num / 100000000).toFixed(2) + "亿";
        } else if (num >= 10000) {
          // 超过万，转换为万
          return (num / 10000).toFixed(1) + "万";
        }
        return num.toString();
      }

      const formattedData = [];

      // 处理 public_sentiment_prefix 数据
      if (
        myData.public_sentiment_prefix &&
        myData.public_sentiment_prefix.healthy_data
      ) {
        state.IndicesStatus.find((item) => {
          if (item.name === "public_sentiment_prefix") {
            item.num = formatNumber(
              myData.public_sentiment_prefix.healthy_data
            );
          }
        });

        state.allDataNum += myData.public_sentiment_prefix.healthy_data;
      }

      // 处理 group_content_data_prefix_telegram 数据
      if (
        myData.group_content_data_prefix_telegram &&
        myData.group_content_data_prefix_telegram.healthy_data
      ) {
        state.IndicesStatus.find((item) => {
          if (item.name === "group_content_data_prefix_telegram") {
            item.num = formatNumber(
              myData.group_content_data_prefix_telegram.healthy_data
            );
          }
        });

        state.allDataNum +=
          myData.group_content_data_prefix_telegram.healthy_data;
      }
      // 处理 social_platform_timeline_prefix_facebook 数据
      if (
        myData.social_platform_timeline_prefix_facebook &&
        myData.social_platform_timeline_prefix_facebook.healthy_data
      ) {
        state.IndicesStatus.find((item) => {
          if (item.name === "social_platform_timeline_prefix_facebook") {
            item.num = formatNumber(
              myData.social_platform_timeline_prefix_facebook.healthy_data
            );
          }
        });

        state.allDataNum +=
          myData.social_platform_timeline_prefix_facebook.healthy_data;
      }
      // 处理 social_platform_timeline_prefix_linkedin 数据
      if (
        myData.social_platform_timeline_prefix_linkedin &&
        myData.social_platform_timeline_prefix_linkedin.healthy_data
      ) {
        state.IndicesStatus.find((item) => {
          if (item.name === "social_platform_timeline_prefix_linkedin") {
            item.num = formatNumber(
              myData.social_platform_timeline_prefix_linkedin.healthy_data
            );
          }
        });

        state.allDataNum +=
          myData.social_platform_timeline_prefix_linkedin.healthy_data;
      }
      // 处理 social_platform_timeline_prefix_twitter 数据
      if (
        myData.social_platform_timeline_prefix_twitter &&
        myData.social_platform_timeline_prefix_twitter.healthy_data
      ) {
        state.IndicesStatus.find((item) => {
          if (item.name === "social_platform_timeline_prefix_twitter") {
            item.num = formatNumber(
              myData.social_platform_timeline_prefix_twitter.healthy_data
            );
          }
        });

        state.allDataNum +=
          myData.social_platform_timeline_prefix_twitter.healthy_data;
      }
      // 在更新状态之前进行去重处理
      const uniqueData = formattedData.filter(
        (item, index, self) =>
          index === self.findIndex((t) => t.name === item.name)
      );

      // 保存当前的社工库数据
      const socialWorkData = state.IndicesStatus.find(
        (item) => item.name === "social_work_library"
      );

      // 更新状态使用去重后的数据
      // state.IndicesStatus = uniqueData;

      // 如果存在社工库数据，检查是否重复后再添加
      /*  if (
        socialWorkData &&
        !uniqueData.some((item) => item.name === "social_work_library")
      ) {
        state.IndicesStatus.push(socialWorkData);
      } */

      console.log("state.IndicesStatus", state.IndicesStatus);
    },
    setHiddenStatusSessionList(state, v) {
      state.hiddenStatusSessionList = v;
    },
    setSendTools(state, v) {
      state.sendTools = v;
    },
    setShowTools(state, v) {
      state.showTools = v;
    },
    setSessionList(state, v) {
      state.sessionList = v;
      console.log("sendFn", v);
    },
    setInitSessionListGroups(state) {
      state.sessionListGroups = {
        今天: [],
        七天内: [],
        "30天内": [],
        "60天内": [],
        更长时间: [],
      };
    },
    setLastRowKey(state, v) {
      state.lastRowKey = v;
    },
    //新建对话
    handleCreateSession(state, v) {
      state.sessionId = v;
    },
  },
  actions: {
    //获取社工库数量
    getAllPathDataNum({ state, commit }, v) {
      // 数值单位转换函数
      function formatNumber(num) {
        if (num >= 10000000) {
          // 超过一千万，转换为亿
          return (num / 100000000).toFixed(2) + "亿";
        } else if (num >= 10000) {
          // 超过万，转换为万
          return (num / 10000).toFixed(1) + "万";
        }
        return num.toString();
      }
      window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            msg: {
              "/etc/kappa/hbase_query_data/path_count_num": "",
            },
          },
        ],
        (res) => {
          const resData = res["/etc/kappa/hbase_query_data/path_count_num"];
          const countNum = resData.count_num_obj;
          let andNum = 0;
          for (const key in countNum) {
            const item = countNum[key];
            let num = (item.d?.count ?? 0) + (item.r?.count ?? 0);
            andNum += num;
          }
          for (let i = 0; i < state.IndicesStatus.length; i++) {
            if (state.IndicesStatus[i].name === "social_work_library") {
              state.IndicesStatus[i].num = formatNumber(andNum);
            }
          }
          /* state.IndicesStatus.push({
            name: "social_work_library",
            num: formatNumber(andNum),
            src: require("@/assets/images/dataImport/社工库.png"),
          }); */
          state.allDataNum += andNum;
        }
      );
    },
    //提前调用mcp工具
    getCallMcpTool({ state, commit }, v) {
      window.main.$ai_socket.sendData(
        "Api.McpServers.Call",
        [
          {
            head: {
              // session_id: window.main.$store.state.userInfo.sessio_id,
            },
            msg: {
              server_name: "common_tools_server",
              tool_name: "who_am_i",
              tool_args: {},
            },
          },
        ],
        (res) => {
          console.log("提前调用mcp工具", res);
          state.mcpCall = res.content;
        }
      );
    },
    //获取工具列表
    getToolsList({ state, commit }, v) {
      window.main.$ai_socket.sendData(
        "Api.McpServers.List",
        [
          {
            head: {
              // session_id: window.main.$store.state.userInfo.sessio_id,
            },
          },
        ],
        (res) => {
          console.log("获取工具列表", res);
          let arr = [];
          for (let str in res) {
            if (str === "common_tools_server" || str === "main_server") {
              for (let stra in res[str].tools) {
                arr.push({
                  function: res[str].tools[stra].function,
                  type: res[str].tools[stra].type,
                });
              }
            }
          }
          console.log("获取工具列表a", arr);
          state.toolsList = arr;
        }
      );
    },
    //获取数据总量统计
    sendIndicesStatus({ state, commit }, v) {
      window.main.$main_socket.sendData(
        "Api.Overview.IndicesStatus",
        [
          {
            head: {
              session_id: window.main.$store.state.userInfo.session_id,
            },
          },
        ],
        "chat/setIndicesStatus"
      );
    },
    getHistory({ state, commit }, v) {
      // 计算时间差（天数）
      function getDayDiff(timestamp) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const targetDate = new Date(timestamp);
        targetDate.setHours(0, 0, 0, 0);
        return Math.floor((today - targetDate) / 86400000); // 86400000 = 24*60*60*1000
      }
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {
              size: 200,
              row_key: state.lastRowKey,
            },
            msg: {
              table: "deepseek_sessions",
              prefix: "",
              type: "username",
              relation: "", // "936;history", //state.nowCollect.id + ";" + state.collectTabsActiveName
            },
          },
        ],
        (res) => {
          console.log("ceshires555", res);
          if (res.length > 0) {
            let arr = [];
            arr.push(res[res.length - 1].row);
            state.lastRowKey = arr;
            state.sessionList = state.sessionList.concat(res);
            res.forEach((item) => {
              const days = getDayDiff(parseInt(item.columnValues.d.timestamp));
              if (days === 0) {
                state.sessionListGroups["今天"].push(item);
              } else if (days <= 7) {
                state.sessionListGroups["七天内"].push(item);
              } else if (days <= 30) {
                state.sessionListGroups["30天内"].push(item);
              } else if (days <= 60) {
                state.sessionListGroups["60天内"].push(item);
              } else {
                state.sessionListGroups["更长时间"].push(item);
              }
            });
            console.log("ceshiressessionList", state.sessionList);
            window.main.$store.dispatch("chat/getHistory");
          }
        }
      );
    },
  },
  modules: {},
};
