<template>
  <div class="docBox">
    <div class="docAside">
      <div class="header">
        文档批次
      </div>
      <div 
        class="batchTree"
        v-loading="batchTreeLoading"
      >
        <el-tree
          :data="batchTree"
          ref="batchTreeRef"
          node-key="id"
          :props="batchTreeProps"
          show-checkbox
          default-expand-all
          @check="batchTreeCheck"
        >
          <div
            :title="node.label"
            class="treeNode"
            slot-scope="{ node }"
          >
            <i 
              :class="node.data.icon"
            ></i>
            <span
              class="treeTitle"
            >
              {{ node.label }}
            </span>
          </div>
        </el-tree>
      </div>
    </div>
    <div class="docMain">
      <div class="header">
        <span>
          文档内容
        </span>
        <el-button
          v-if="!docAllSelected"
          type="primary" 
          size="mini"
          class="button"
          @click="docSelectAll(true)"
        >
          全选
        </el-button>
        <el-button
          v-else
          type="primary" 
          size="mini"
          class="button"
          @click="docSelectAll(false)"
        >
          取消全选
        </el-button>
        <el-button 
          v-if="selectedDoc.length"
          type="success" 
          size="mini"
          class="button"
          @click="batchCollect"
        >
          收藏
        </el-button>
      </div>
      <div class="main">
        <div class="doc">
          <el-empty 
            v-if="!docList.length"
            description="没有搜索到邮件"
          ></el-empty>
          <div class="list">
            <div
              v-for="doc in docList"
              :key="doc._id"
              class="docOne"
            >
              <div class="checkBox">
                <input
                  style="cursor: pointer"
                  type="checkbox"
                  :value="doc"
                  v-model="selectedDoc"
                  @change="docSelectOne(doc)"
                />
              </div>
              <div
                class="document"
              >
                <div 
                  class="title"
                  @click="docShowDetail(doc)"
                >
                  {{doc._source.file_name}}
                </div>
                <div class="info">
                  <!-- <div 
                    v-if="doc._source.type=='message/rfc822'"
                    class="to"
                  >
                    <span class="key">收件人:</span>
                    <span class="value">{{doc._source.email_tos}}</span>
                  </div> -->
                  <div 
                    v-if="doc._source.type=='message/rfc822'"
                    class="from"
                  >
                    <span class="key">发件人: </span>
                    <span 
                      :title="doc._source.email_from"
                      class="value"
                    >
                      {{doc._source.email_from.split(' ').join('')}}
                    </span>
                  </div>
                  <div class="time">
                    <span class="key">时间: </span>
                    <span class="value">2025-01-02 03:04:05</span>
                  </div>
                </div>
                <div class="content">
                  {{doc._source.content_article}}
                </div>
              </div>
              <div class="button">
                <el-button 
                  type="success" 
                  size="mini"
                  class="btn"
                  @click="collectOne(doc)"
                >
                  收藏
                </el-button>
              </div>
            </div>
          </div>
          <div class="pagination">
            <el-pagination
              layout="pager"
              :page-size="20"
              :total="docTotal"
              @current-change="pageChange"
            >
            </el-pagination>
          </div>
        </div>
        <div class="filter">
          <div class="mail">
            <div class="title">
              <span>
                邮箱
              </span>
              <el-button 
                type="primary" 
                icon="el-icon-plus"
                size="mini"
                class="button"
                @click="showAddMailDialog"
              ></el-button>
              <el-button 
                v-if="checkedMail.length"
                type="danger" 
                icon="el-icon-delete"
                size="mini"
                class="button"
                @click="deleteMail"
              ></el-button>
            </div>
            <div 
              v-if="gotMailList"
              class="mailList"
            >
              <!-- <el-checkbox 
                :indeterminate="mailIndeterminate" 
                v-model="mailCheckAll" 
                @change="mailCheckAllChange"
              >
                全选
              </el-checkbox>
              <div style="margin: 15px 0;"></div> -->
              <el-checkbox-group 
                v-model="checkedMail"
                :min="0"
                :max="1"
                @change="checkedMailChange"
                class="checkBoxGroup"
              >
                <el-checkbox 
                  v-for="mail in mailList" 
                  :label="mail" 
                  :key="mail.id"
                  class="checkBox"
                  :title="mail.mail"
                >
                  {{mail.mail}}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="time">
            <div class="title">
              时间
            </div>
            <div class="datePicker">
              <el-date-picker
                v-model="docTimeRange"
                type="daterange"
                align="right"
                size="small"
                unlink-panels
                range-separator=" - "
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                value-format="timestamp"
                @change="timeRangeChange"
              >
              </el-date-picker>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      :visible.sync="showCollectDialog"
      :close-on-click-modal="false"
      title="选取收藏目录"
      top="10px"
      width="40%"
      append-to-body
    >
      <div style="width: 95%">
        <collectTree
          :listType="'username'"
          :tableName="'favorites_data'"
          :getCollect="setCollect"
        ></collectTree>
      </div>
    </el-dialog>

    <el-dialog
      title="添加邮箱"
      :visible.sync="addMaildialogVisible"
      width="40%"
      :modal-append-to-body="false"
      :close-on-click-modal="false"
      :before-close="addMailDialogClose"
    >
      <div
        v-loading="addMailLoading" 
        class="addMailBox"
      >
        <span class="title">
          邮箱：
        </span>
        <el-select 
          v-model="addMailAddr" 
          placeholder="请输入邮箱"
          filterable
          allow-create
          clearable 
          class="select"
        >
          <el-option
            v-for="person in keyPersonList"
            :key="person.id"
            :label="person.mail"
            :value="person.mail"
          >
            <div class="mailCard">
              <div class="img">
                <el-avatar 
                  shape="square" 
                  :size="40" 
                  :src="require('@/assets/images/user.png')"
                ></el-avatar>
              </div>
              <div class="info">
                <div class="name">
                  <span class="key">
                    姓名：
                  </span>
                  <span
                    :title="person.name" 
                    class="value"
                  >
                    {{ person.name }}
                  </span>
                </div>
                <div class="mail">
                  <span class="key">
                    邮箱：
                  </span>
                  <span
                    :title="person.mail" 
                    class="value"
                  >
                    {{ person.mail }}
                  </span>
                </div>
              </div>
            </div>
          </el-option>
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addMailDialogClose">取 消</el-button>
        <el-button type="primary" @click="addMaildialogCommit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import { collectTree } from "@/layout/components";
export default {
  components: {
    collectTree,
  },
  data() {
    return {
      batchTreeLoading: true,
      batchTreeProps: {
        children: 'children',
        label: 'label'
      },
      docAllSelected: false,
      showCollectDialog: false,
      collectNum: 0,
      selectedDoc:[],
      // docTimeRange: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now(); // 禁用未来的日期
        },
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      mailCheckAll: false,
      mailIndeterminate: false,
      checkedMail: [],
      addMaildialogVisible: false,
      addMailLoading: true,
      addMailAddr:"",
    };
  },
  created() {
    this.initTree()
  },
  mounted() {
  },
  destroyed(){
    console.log("<destroyed>");
    this.batchTreeLoading = true
    this.clearTree()
    this.clearListTrue()
    this.clearDocFrom()
    this.clearDoc()
    this.clearMail()
    this.clearPerson()
    this.clearSelectMail()
  },
  computed: {
    ...mapState({
      tabsActiveName: (state) => state.search.searchList.tabsActiveName,
      gotBatchTree: (state) => state.search.document.gotBatchTree,
      batchTree: (state) => state.search.document.batchTree,
      keyPersonList: (state) => state.search.document.keyPersonList,
      gotKeyPerson: (state) => state.search.document.gotKeyPerson,
      mailList: (state) => state.search.document.mailList,
      gotMailList: (state) => state.search.document.gotMailList,
      docTotal: (state) => state.search.document.docTotal,
      docList: (state) => state.search.document.docList,
      gotDocList: (state) => state.search.document.gotDocList,
    }),
    batchIdList: {
      get() {
        return this.$store.state.search.document.batchIdList;
      },
      set(val) {
        this.setBatchId(val)
      },
    },
    selectMail: {
      get() {
        return this.$store.state.search.document.selectMail;
      },
      set(val) {
        this.setSelectMail(val)
      },
    },
    docTimeRange: {
      get() {
        return this.$store.state.search.document.docTimeRange;
      },
      set(val) {
        this.setTimeRange(val)
      },
    },
  },
  watch: {
    gotBatchTree: {
      handler(val, oldVal) {
        if (this.gotBatchTree == true) {
          console.log("<gotBatchTree> batchTree:", this.batchTree);
          // this.$refs.batchTree.setCheckedNodes(this.batchTree);
          this.$refs.batchTreeRef.setCheckedKeys(['0'])
          this.batchTreeLoading = false
        }
      },
      deep: true,
      immediate: true,
    },
    gotKeyPerson: {
      handler(val, oldVal) {
        if (this.gotKeyPerson == true) {
          console.log("<gotKeyPerson> keyPersonList:", this.keyPersonList);
          this.addMailLoading = false
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    ...mapMutations({
      clearTree: "search/document/clearTree",
      clearListTrue: "search/document/clearListTrue",
      clearDoc: "search/document/clearDoc",
      clearMail: "search/document/clearMail",
      clearPerson: "search/document/clearPerson",
      clearBatchIdList: "search/document/clearBatchIdList",
      setBatchId: "search/document/setBatchId",
      setSelectMail: "search/document/setSelectMail",
      clearSelectMail: "search/document/clearSelectMail",
      setTimeRange: "search/document/setTimeRange",
      clearDocFrom: "search/document/clearDocFrom",
      setDocFrom: "search/document/setDocFrom",
    }),

    ...mapActions({
      getBatchTree: "search/document/getBatchTree",
      getListTrue: "search/document/getListTrue",
      getDocList: "search/document/getDocList",
      getKeyPerson: "search/document/getKeyPerson",
      createMailOss: "search/document/createMailOss",
      dropMailOss: "search/document/dropMailOss",
      addMail: "search/document/addMail",
      delMail: "search/document/delMail",
      getMailList: "search/document/getMailList",
    }),

    search(){
      console.log("<search>");
      
      // this.clearDoc()
      this.docSelectAll(false)
      this.getDocList()
    },

    initTree (){
      console.log("<initTree>");
      
      // this.createMailOss()
      // this.dropMailOss()
      // this.delMail()
      this.getListTrue()
      this.getMailList()
      this.clearTree()
      this.getBatchTree({
        row: [],
        fatherId: "",
        type:""
      })
    },

    batchTreeCheck(){
      let checkNode = this.$refs.batchTreeRef.getCheckedNodes()
      console.log("<batchTreeCheck> checkNode:", checkNode)

      let tmpId = []
      checkNode.forEach(node=>{
        if(node.row){
          tmpId.push(window.main.$md5(node.row))
        }
      })

      console.log("<batchTreeCheck> tmpId:", tmpId)
      this.batchIdList = tmpId
    },

    docSelectAll(val){
      console.log("<docSelectAll> val:", val)
      
      this.docAllSelected = val
      this.selectedDoc = val?this.docList:[]

      console.log("<docSelectAll> selectedDoc:", this.selectedDoc)
    },

    batchCollect(){
      console.log("<batchCollect> selectedDoc:", this.selectedDoc)
      
      this.showCollectDialog = true;
    },

    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString();
      let random = new Array(
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n"
      );
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13);
        soleValue += random[index];
      }
      return soleValue;
    },
    
    setCollect(data) {
      console.log("<setCollect> data:", data)

      this.$confirm("确定选择此目录收藏?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
      .then(() => {
        this.selectedDoc.forEach((element) => {
          let prefix =
            1e13 -
            Math.round(new Date().getTime() / 1000) +
            data.id +
            this.reduceNumber();
          window.main.$main_socket.sendData(
            "Api.Search.SearchPrefixTable.AddData",
            [
              {
                msg: {
                  type: "username",
                  authority:
                    window.main.$store.state.userInfo.userinfo.authority,
                  username:
                    window.main.$store.state.userInfo.userinfo.username,
                  table: "favorites_data",
                  prefix,
                  relation: data.id + ";" + this.tabsActiveName,
                  data: {
                    data: {
                      file_data: element,
                    },
                  },
                },
              },
            ],
            (res) => {
              console.log("<setCollect> res:", res)
              if (res?.status === "ok") {
                this.collectNum++;
                if (this.collectNum === this.selectedDoc.length) {
                  this.$message.success("收藏成功!");
                  this.collectNum = 0;

                  this.docSelectAll(false)
                  this.showCollectDialog = false;
                }
              }
            }
          );
        });
      })
      .catch((err) => {
        this.$message({
          type: "info",
          message: "已取消收藏",
        });
      });
    },

    collectOne(doc){
      console.log("<collectOne> doc:", doc)
    },

    docSelectOne(doc){
      console.log("<docSelectOne> doc:", doc)
      console.log("<docSelectOne> selectedDoc:", this.selectedDoc)

      if (this.selectedDoc.length == this.docList.length){
        this.docAllSelected = true
      }else {
        this.docAllSelected = false
      }
    },

    docShowDetail(doc){
      console.log("<docShowDetail> doc:", doc)

      let routeData = this.$router.resolve({
        name: "docDetail",
        query: {
          id: doc._id,
          index: doc._index
        },
      });
      window.open(routeData.href, "_blank");
    },

    pageChange(val){
      console.log("<docSelectOne> val:", val)

      this.setDocFrom((val - 1) * 20)
      this.search()
    },

    mailCheckAllChange(val){
      console.log("<mailCheckAllChange> val:", val)
      console.log("<mailCheckAllChange> mailList:", this.mailList)

      this.checkedMail = val ? this.mailList : []
      this.mailIndeterminate = false
      this.$forceUpdate()

      console.log("<mailCheckAllChange> checkedMail:", this.checkedMail)
      if (this.checkedMail.length){
        let tmpMail = []
        this.checkedMail.forEach(mail=>{
          tmpMail.push(mail.mail)
        })
        console.log("<mailCheckAllChange> tmpMail:", tmpMail)
        this.selectMail = tmpMail
      } else {
        this.selectMail = []
      }
    },

    checkedMailChange(value){
      console.log("<checkedMailChange> value:", value)
      console.log("<checkedMailChange> checkedMail:", this.checkedMail)

      let checkedCount = value.length;
      this.mailCheckAll = checkedCount === this.mailList.length;
      this.mailIndeterminate = checkedCount > 0 && checkedCount < this.mailList.length;

      if (this.checkedMail.length){
        let tmpMail = []
        this.checkedMail.forEach(mail=>{
          tmpMail.push(mail.mail)
        })
        console.log("<checkedMailChange> tmpMail:", tmpMail)
        this.selectMail = tmpMail
      }else{
        this.selectMail = []
      }

      this.clearDoc()
      this.search()
    },

    deleteMail(){
      console.log("<deleteMail> checkedMail", this.checkedMail);

      let tips = '删除< ' + this.checkedMail[0].mail + ' >这个邮箱, 是否继续?'
      this.$confirm(tips, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.delMail({
          id: this.checkedMail[0].id
        })
        this.checkedMail = []
        this.checkedMailChange([])
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });  
      });
    },

    showAddMailDialog(){
      console.log("<showAddMailDialog>");

      this.getKeyPerson({})
      this.addMailAddr = ""
      this.addMailLoading = true
      this.addMaildialogVisible = true
    },

    addMailDialogClose(){
      console.log("<addMailDialogClose>");

      this.addMaildialogVisible = false
      this.addMailAddr = ""
      this.addMailLoading = true
      this.clearPerson ()
    },

    isValidEmail(email) {
      const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      return emailRegex.test(email);
    },

    addMaildialogCommit (){
      console.log("<addMaildialogCommit> addMailAddr:", this.addMailAddr);

      if (!this.addMailAddr){
        window.main.$message.closeAll()
				window.main.$message.error("请填写邮箱!");

        return
      }else if (!this.isValidEmail(this.addMailAddr)){
        window.main.$message.closeAll()
				window.main.$message.error("请填写正确的邮箱!");

        return
      }

      this.addMail({
        mail:this.addMailAddr
      })

      this.addMailDialogClose()
    },

    timeRangeChange(){
      console.log("<timeRangeChange> docTimeRange:", this.docTimeRange);

      this.clearDoc()
      this.search()
    }
  },
};
</script>

<style scoped lang="scss">
.docBox{
  height: 735px;
  width: 100%;
  display: flex;
  flex-direction: row;
  .docAside{
    height: 100%;
    width: 25%;
    margin: 0px 3px 0px 6px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    .header{
      height: 30px;
      line-height: 30px;
      margin-left: 5px;
      font-size: 18px;
      font-weight: bold;
    }
    .batchTree{
      height: 705px;
      width: 100%;
      overflow-y: auto;
      .treeNode{
        width: 100%;
        margin-top: 5px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size:15px;
        .treeTitle{
          margin-left: 5px;
          display: block;
          // width: 150px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .docMain{
    height: 100%;
    width: 75%;
    margin: 0px 6px 0px 3px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    .header{
      height: 30px;
      width: 100%;
      line-height: 30px;
      font-size: 18px;
      font-weight: bold;
      margin-left: 5px;
      .button{
        height: 20px;
        width: auto;
        line-height: 18px;
        margin-left: 10px;
        font-size: 13px;
        padding: 0 3px;
      }
      
    }
    .main{
      height: 705px;
      width: 100%;
      display: flex;
      flex-direction: row;
      .doc{
        height: 705px;
        width: 60%;
        .list{
          height: 670px;
          width: 98%;
          overflow-y: auto;
          .docOne{
            height: 130px;
            width: 97%;
            display: flex;
            flex-direction: row;
            border: 1px solid #ebeef5;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            margin: 10px 5px;
            .checkBox{
              height: 100%;
              width: 50px;
              display: flex;
              align-content: center;
              justify-content: center;
              flex-direction: column;
            }
            .document{
              height: 100%;
              flex-grow: 1;
              .title{
                height: 20px;
                line-height: 20px;
                font-weight: bold;
                text-decoration: underline;
                cursor:pointer;
              }
              .info{
                height: 20px;
                line-height: 20px;
                display: flex;
                flex-direction: row;
                .from{
                  width: 200px;
                  display: flex;
                  .key{
                    color: #333
                  }
                  .value{
                    color: #909399;
                    margin-left: 5px;
                    max-width: 140px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }
                .time{
                  .key{
                    color: #333
                  }
                  .value{
                    color: #909399;
                    margin-left: 5px;
                  }
                }
              }
              .content{
                max-height: 80px;
                width: 360px;
                margin-top: 5px;
                overflow: hidden;
              }
            }
            .button{
              height: 100%;
              width: 70px;
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
              .btn{
                height: 20px;
                width: 40px;
                line-height: 18px;
                font-size: 13px;
                padding: 0px !important;
              }
            }
          }
        }
        .pagination{
          // height: 30px;
          width: 100%;
          display: flex;
          justify-content: center;
        }
      }
      .filter{
        height: 705px;
        width: 40%;
        .time{
          height: 90px;
          width: 100%;
          margin-top:10px;
          .title{
            font-size: 15px;
            font-weight: bold;
            height: 30px;
            line-height: 30px;
          }
          .datePicker{
            margin-top:5px
          }
        }
        .mail{
          height: 50%;;
          width: 100%;
          border-bottom: 1px solid #ebeef5;
          .title{
            font-size: 15px;
            font-weight: bold;
            height: 30px;
            line-height: 30px;
            .button{
              height: 20px;
              width: 30px;
              margin-left: 10px;
              font-size: 14px;
              padding: 0px 8px!important;
            }
          }
          .mailList{
            margin-top: 5px;
            height: 90%;
            overflow-y: auto;
            .checkBoxGroup{
              display: flex;
              flex-direction: row;
              flex-wrap: wrap;
              gap: 10px;
              .checkBox{
                width: 170px;
                margin-right: 0px;
                overflow: hidden;
              }
            }
          }
        }
      }
    }
  }
}
.addMailBox{
  height: 300px;
  display: flex;
  justify-content: center;
  .title{
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    font-weight: 600;
  }
  .select{
    width: 300px;
    margin-left: 20px
  }
}
.mailCard{
  height: 60px;
  width: 100%;
  padding: 10px;
  display: flex;
  flex-direction: row;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  .img{
    height: 100%;
  }
  .info{
    height: 100%;
    margin-left: 10px;
    .name{
      height: 20px;
      line-height: 20px;
      .key{
        color:#333
      }
      .value{
        max-width: 140px;
        display: inline-flex;
        overflow: hidden;
        color:#909399
      }
    }
    .mail{
      height: 20px;
      line-height: 20px;
      .key{
        color:#333
      }
      .value{
        max-width: 140px;
        display: inline-flex;
        overflow: hidden;
        color:#909399
      }
    }

  }
}

.el-select-dropdown__item{
  width: 100%;
  height: 70px;
  display: flex;
  align-items: center;
}
</style>

