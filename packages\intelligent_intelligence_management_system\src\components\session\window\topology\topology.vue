<template>
  <div class="topologyLay">
    <div class="topologyLay_left">
      <!-- 个人信息 -->
      <div class="person-card" v-if="personInfo">
        <div class="topologyLay_left_avatar">
          <div class="avatar-img">
            <img
              shape="square"
              :src="getAvatarSrc(personInfo.avatar)"
              class="custom-avatar"
            />
          </div>
          <div class="avatar-info">
            <div class="info-row">
              <span class="info-label">姓名：</span
              ><span class="info-value" :title="personInfo.name">{{
                personInfo.name
              }}</span>
            </div>
            <div class="info-row sex-age-row">
              <span>
                <span class="info-label">性别：</span>
                <span class="info-value">{{ personInfo.sex }}</span>
              </span>
            </div>
            <div class="info-row sex-age-row">
              <span class="info-age">
                <span class="info-label">年龄：</span>
                <span class="info-value">{{ personInfo.age }}</span>
              </span>
            </div>
            <div class="info-row">
              <span class="info-label">手机号：</span
              ><span class="info-value" :title="personInfo.phone">{{
                personInfo.phone
              }}</span>
            </div>
          </div>
        </div>
        <div class="other relation-collapse-content" style="overflow-y: auto">
          <div class="relation-row">
            <span class="relation-label">出生日期：</span>
            <span class="relation-value">{{ personInfo.dateBirth }}</span>
          </div>
          <div class="relation-row">
            <span class="relation-label">身份证号：</span>
            <span class="relation-value">{{ personInfo.identity }}</span>
          </div>
          <div class="relation-row">
            <span class="relation-label">邮箱：</span>
            <span class="relation-value">{{ personInfo.email }}</span>
          </div>
          <div class="relation-row">
            <span
              class="relation-label"
              v-if="personInfo.hasOwnProperty('telegramIds')"
              >telegramIds：</span
            >
            <span
              class="relation-value"
              v-for="(itemD, ind) in personInfo.telegramIds"
              :key="itemD + ind"
              >{{ itemD }}</span
            >
          </div>
          <div class="relation-row">
            <span
              class="relation-label"
              v-if="personInfo.hasOwnProperty('twitterIds')"
              >twitterId:</span
            >
            <span
              class="relation-value"
              v-for="(itemD, ind) in personInfo.twitterIds"
              :key="itemD + ind"
              >{{ itemD }}</span
            >
          </div>
          <div
            class="relation-row"
            v-if="personInfo.hasOwnProperty('facebookIds')"
          >
            <span class="relation-label">facebookId:</span>
            <span
              class="relation-value"
              v-for="(itemD, ind) in personInfo.facebookIds"
              :key="itemD + ind"
              >{{ itemD }}</span
            >
          </div>
          <div
            class="relation-row"
            v-if="personInfo.hasOwnProperty('linkedInIds')"
          >
            <span class="relation-label">linkedInId:</span>
            <span
              class="relation-value"
              v-for="(itemD, ind) in personInfo.linkedInIds"
              :key="itemD + ind"
              >{{ itemD }}</span
            >
          </div>
          <div
            class="relation-row"
            v-if="personInfo.hasOwnProperty('phoneNumbers')"
          >
            <span class="relation-label">phoneNumber:</span>
            <span
              class="relation-value"
              v-for="(itemD, ind) in personInfo.phoneNumbers"
              :key="itemD + ind"
              >{{ itemD }}</span
            >
          </div>
          <div
            v-for="(key, value, index) in otherInfo"
            :key="'other' + index"
            class="relation-row"
          >
            <span class="relation-label">{{ key }}：</span>
            <span class="relation-value">{{ value }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="topologyLay_right">
      <!-- 遮罩层 - 只在图表容器内显示 -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p class="loading-text">正在加载关系拓扑图...</p>
          <div class="loading-progress">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: loadingProgress + '%' }"
              ></div>
            </div>
            <span class="progress-text">{{ loadingProgress }}%</span>
          </div>
          <p class="loading-step">{{ currentLoadingStep }}</p>
          <button
            v-if="loadingError"
            @click="retryLoading"
            class="retry-button"
          >
            重新加载
          </button>
        </div>
      </div>
      <div
        :ref="'chartContainer' + myId"
        style="width: 100%; height: 100%"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "topology",
  props: {
    myData: { type: Object, required: true },
    myId: { type: String, required: true },
  },
  data() {
    return {
      loadingError: false,
      loading: true,
      resizeObserver: null,
      loadingProgress: 0,
      currentLoadingStep: "初始化数据...",
      personalRelations: [],
      personRelationSearchList: [],
      organizationRelations: [],
      // 人员基本信息
      personInfo: null,
      // 其他信息
      otherInfo: [],
      selectPersonData: null,
      telegram: "",
      twitter: "",
      facebook: "",
      linkedin: "",
      personName: "",
      identity: "",
      phone: "",
      email: "",
    };
  },
  created() {},
  mounted() {
    this.selectPerson(this.myData);
    // 确保DOM已经渲染完成后再开始加载
    this.$nextTick(() => {
      // 启动进度条动画
      this.startProgressAnimation();

      // 设置ResizeObserver监听容器大小变化
      this.setupResizeObserver();

      // 延迟4秒后加载ECharts
      setTimeout(() => {
        this.tryInitECharts();
      }, 6000);
    });
  },
  watch: {
    personInfo: {
      handler(newVal) {
        if (newVal) {
          console.log("personInfo", newVal);
          this.$nextTick();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    retryLoading() {
      this.loading = true;
      this.loadingError = false;
      this.loadingProgress = 0;
      this.currentLoadingStep = "重新加载...";

      // 重新启动进度条动画
      this.startProgressAnimation();

      // 使用新的重试机制
      setTimeout(() => {
        this.tryInitECharts();
      }, 2000);
    },
    getNodeTypeLabel(category) {
      const labelMap = {
        center: "中心人物",
        personal: "个人关系",
        organization: "组织关系",
        "twitter-group": "Twitter用户组",
        "twitter-user": "Twitter用户",
        "twitter-followers": "Twitter粉丝",
        "twitter-following": "Twitter关注",
        "telegram-group": "Telegram用户组",
        "telegram-user": "Telegram用户",
        "linkedin-group": "LinkedIn用户组",
        "linkedin-user": "LinkedIn用户",
        "facebook-group": "Facebook用户组",
        "facebook-user": "Facebook用户",
        "facebook-friends": "Facebook朋友",
        "facebook-followers": "Facebook关注者",
        "email-recipient": "邮件收件人",
        "email-sender": "邮件发件人",
        "twitter-user-group": "Twitter用户",
        "email-group": "邮箱",
        "email-recipient-group": "收信邮箱",
        "email-sender-group": "发信邮箱",
        "linkedin-user-group": "LinkedIn用户",
        "facebook-user-group": "Facebook用户",
      };
      return labelMap[category] || "未知类型";
    },
    initChart(echarts) {
      try {
        // 如果已经存在图表实例，先销毁
        if (this.chart) {
          this.chart.dispose();
          this.chart = null;
        }

        // 容器已经在tryInitECharts中验证过了
        const container = this.$refs["chartContainer" + this.myId];
        this.chart = echarts.init(container);

        const chartData = this.getChartData();

        const option = {
          title: {
            text: "关系网络拓扑图",
            top: "top",
            left: "center",
            textStyle: {
              fontSize: 18,
              fontWeight: "bold",
            },
          },
          tooltip: {
            trigger: "item",
            formatter: (params) => {
              if (params.dataType === "node") {
                const data = params.data.data || {};
                let content = `<strong>${params.data.name}</strong><br/>`;
                content += `类型: ${this.getNodeTypeLabel(
                  params.data.category
                )}<br/>`;

                if (data.age) content += `年龄: ${data.age}<br/>`;
                if (data.phone) content += `电话: ${data.phone}<br/>`;
                if (data.followers_count)
                  content += `粉丝数: ${data.followers_count}<br/>`;
                if (data.nickname) content += `昵称: ${data.nickname}<br/>`;
                if (data.belong) content += `所属: ${data.belong}<br/>`;
                if (data.relationship)
                  content += `关系: ${data.relationship}<br/>`;

                return content;
              } else if (params.dataType === "edge") {
                return `关系: ${params.data.name}`;
              }
            },
          },
          legend: {
            show: false,
          },
          animationDuration: 1500,
          animationEasingUpdate: "quinticInOut",
          series: [
            {
              type: "graph",
              layout: "force",
              data: chartData.nodes,
              links: chartData.links,
              categories: [
                { name: "center" },
                { name: "personal-group" },
                { name: "personal" },
                { name: "org-group" },
                { name: "organization" },
                { name: "twitter-user-group" },
                { name: "twitter-user" },
                { name: "twitter-followers" },
                { name: "twitter-follower" },
                { name: "twitter-following" },
                { name: "twitter-following-data" },
                { name: "linkedin-user-group" },
                { name: "linkedin-user" },
                { name: "facebook-user-group" },
                { name: "facebook-user" },
                { name: "telegram-group" },
                { name: "telegram-user" },
                { name: "email-group" },
                { name: "email-recipient-group" },
                { name: "email-recipient" },
                { name: "email-sender-group" },
                { name: "email-sender" },
              ],
              roam: true,
              focusNodeAdjacency: true,
              itemStyle: {
                borderColor: "#fff",
                borderWidth: 2,
                shadowBlur: 10,
                shadowColor: "rgba(0, 0, 0, 0.3)",
              },
              label: {
                show: true,
                position: "bottom",
                formatter: function (params) {
                  // 如果节点有自定义的 label 配置，则使用节点的 formatter
                  if (params.data.label && params.data.label.formatter) {
                    return params.data.label.formatter(params);
                  }
                  return params.name;
                },
                fontSize: 10,
                color: "#333333",
              },
              lineStyle: {
                opacity: 0.8,
                width: 2,
                curveness: 0.1,
              },
              emphasis: {
                focus: "adjacency",
                lineStyle: {
                  width: 4,
                },
              },
              force: {
                repulsion: 600,
                gravity: 0.2,
                edgeLength: [100, 150],
                layoutAnimation: true,
              },
            },
          ],
        };

        this.chart.setOption(option);

        // 添加点击事件
        this.chart.on("click", (params) => {
          if (params.dataType === "node") {
            this.selectedNode = params.data;
          }
        });

        // 响应式处理 - 使用ResizeObserver替代window resize事件
        if (this.resizeObserver && this.$refs["chartContainer" + this.myId]) {
          this.resizeObserver.observe(this.$refs["chartContainer" + this.myId]);
        }

        // 图表加载完成后自动缩小展示
        setTimeout(() => {
          if (this.chart) {
            // 通过调整force布局参数来控制图表大小
            this.chart.setOption({
              series: [
                {
                  force: {
                    repulsion: 600, // 减小斥力，让节点更紧凑
                    gravity: 0.2, // 增加重力，让节点更集中
                    edgeLength: [50, 100], // 减小边长度
                    layoutAnimation: false, // 关闭动画以便立即生效
                  },
                },
              ],
            });

            // 重新开启动画
            setTimeout(() => {
              if (this.chart) {
                this.chart.setOption({
                  series: [
                    {
                      force: {
                        layoutAnimation: true,
                      },
                    },
                  ],
                });
              }
            }, 100);
          }
        }, 1500); // 延迟1.5秒执行，确保图表完全渲染

        console.log("ECharts初始化成功");
      } catch (error) {
        console.error("ECharts初始化失败:", error);
        throw error;
      }
    },
    async tryInitECharts(retryCount = 0) {
      const MAX_RETRIES = 15; // 最多重试15次
      const RETRY_DELAY = 200; // 每次重试间隔200ms

      try {
        // 检查DOM元素是否存在且有尺寸
        const container = this.$refs["chartContainer" + this.myId];

        if (!container) {
          console.log(
            `Chart container not found, retry ${retryCount + 1}/${MAX_RETRIES}`
          );
          this.currentLoadingStep = `等待DOM渲染... (${
            retryCount + 1
          }/${MAX_RETRIES})`;

          if (retryCount < MAX_RETRIES) {
            setTimeout(() => this.tryInitECharts(retryCount + 1), RETRY_DELAY);
            return;
          } else {
            throw new Error("Chart container not found after maximum retries");
          }
        }

        // 检查容器是否有有效尺寸
        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
          console.log(
            `Chart container has zero dimensions, retry ${
              retryCount + 1
            }/${MAX_RETRIES}`
          );
          this.currentLoadingStep = `等待容器尺寸... (${
            retryCount + 1
          }/${MAX_RETRIES})`;

          if (retryCount < MAX_RETRIES) {
            setTimeout(() => this.tryInitECharts(retryCount + 1), RETRY_DELAY);
            return;
          } else {
            throw new Error(
              "Chart container has no valid dimensions after maximum retries"
            );
          }
        }

        // 检查ECharts是否可用
        if (!this.$echarts) {
          console.error("ECharts not available");
          this.currentLoadingStep = "ECharts库未加载...";
          throw new Error("ECharts library not available");
        }

        this.currentLoadingStep = "初始化图表...";
        this.loadingProgress = Math.max(this.loadingProgress, 95);

        // 使用全局引入的 ECharts
        const echarts = this.$echarts;
        this.initChart(echarts);

        this.currentLoadingStep = "图表初始化完成";
        this.loadingProgress = 100;
      } catch (error) {
        console.error("Failed to load ECharts:", error);
        this.currentLoadingStep = "加载失败，使用备用方案...";
        this.loadingError = true;
        this.renderFallbackChart();
      } finally {
        // 无论成功还是失败，都隐藏遮罩层
        setTimeout(() => {
          this.loading = false;
        }, 500); // 延迟500ms隐藏，让用户看到完成状态
      }
    },
    renderFallbackChart() {
      try {
        // 检查DOM元素是否存在
        if (!this.$refs["chartContainer" + this.myId]) {
          console.error("Chart container not found for fallback chart");
          return;
        }

        // 如果ECharts加载失败，显示简单的统计信息
        const container = this.$refs["chartContainer" + this.myId];
        container.innerHTML = `
          <div style="padding: 20px; text-align: center; color: #666;">
            <h3>关系网络拓扑图</h3>
            <p>ECharts加载失败，显示简化版本</p>
            <div style="margin: 20px 0;">
              <div style="display: inline-block; margin: 10px; padding: 10px; background: #f7fafc; border-radius: 8px;">
                <strong>Twitter用户:</strong> ${this.twitterUser.length}
              </div>
              <div style="display: inline-block; margin: 10px; padding: 10px; background: #f7fafc; border-radius: 8px;">
                <strong>个人关系:</strong> ${this.personalRelations.length}
              </div>
              <div style="display: inline-block; margin: 10px; padding: 10px; background: #f7fafc; border-radius: 8px;">
                <strong>组织关系:</strong> ${this.organizationalRelations.length}
              </div>
            </div>
            <p>请点击下方标签页查看详细数据</p>
          </div>
        `;
        console.log("备用图表渲染成功");
      } catch (error) {
        console.error("备用图表渲染失败:", error);
      }
    },
    handleResize() {
      // 防抖处理，避免频繁调用
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }

      this.resizeTimer = setTimeout(() => {
        if (this.chart) {
          console.log("Resizing ECharts chart...");
          this.chart.resize();
        }
      }, 100);
    },
    setupResizeObserver() {
      // 检查浏览器是否支持ResizeObserver
      if (typeof ResizeObserver === "undefined") {
        console.warn(
          "ResizeObserver not supported, falling back to window resize event"
        );
        // 降级到window resize事件
        window.addEventListener("resize", this.handleResize);
        return;
      }

      // 创建ResizeObserver监听容器大小变化
      this.resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          // 检查是否是图表容器
          if (entry.target === this.$refs["chartContainer" + this.myId]) {
            this.handleResize();
            break;
          }
        }
      });

      // 延迟监听，确保DOM已经渲染
      this.$nextTick(() => {
        if (this.$refs["chartContainer" + this.myId]) {
          this.resizeObserver.observe(this.$refs["chartContainer" + this.myId]);
          console.log("ResizeObserver started observing chart container");
        }
      });
    },
    startProgressAnimation() {
      const duration = 2500; // 2.5秒，与延迟加载时间匹配
      const interval = 50; // 每50ms更新一次
      const steps = duration / interval;
      const increment = 100 / steps;

      const loadingSteps = [
        { progress: 20, text: "初始化数据..." },
        { progress: 40, text: "加载Twitter数据..." },
        { progress: 60, text: "加载Telegram数据..." },
        { progress: 80, text: "准备图表组件..." },
        { progress: 100, text: "完成加载..." },
      ];

      const timer = setInterval(() => {
        this.loadingProgress += increment;

        // 更新加载步骤
        const currentStep = loadingSteps.find(
          (step) => this.loadingProgress <= step.progress
        );
        if (currentStep) {
          this.currentLoadingStep = currentStep.text;
        }

        if (this.loadingProgress >= 100) {
          this.loadingProgress = 100;
          this.currentLoadingStep = "完成加载...";
          clearInterval(timer);
        }
      }, interval);
    },
    initData(d) {
      try {
        let data = null;
        if (d) {
          data = d;
        } else {
          data = this.myData;
        }

        this.info = data;
        this.personId = data._id;
        // 适配后的数据结构
        this.personInfo = data._source.params.basic;
        console.log("适配后的数据结构", this.personInfo);
        // 适配后的自定义字段
        this.otherInfo = data._source.params.customFields;
        // 社交媒体信息
        this.socialMedia = { ...data._source.params.media };
        // 人员关系
        // this.personRelation = data._source.params.basic.relation; // basic中的关系
        // 社会关系
        console.log(
          "data._source.params.socialize",
          data._source.params.socialize
        );
        if (
          data._source.params.socialize &&
          data._source.params.socialize.relation &&
          data._source.params.socialize.relation[0].relationName != ""
        ) {
          this.socializeMsg =
            data._source.params.socialize.relation[0].relationName;
        } else {
          this.socializeMsg = [];
        }
      } catch (error) {
        console.error("解析数据时出错:", error);
      }
    },
    // 选择的人员信息
    selectPerson(data) {
      console.log("选择的人员信息", data);
      // 兼容新数据格式
      let p = data._source.params;
      const standardFields = [
        "type",
        "avatar",
        "name",
        "remark",
        "sex",
        "age",
        "phoneNumbers",
        "identity",
        "email",
        "dateBirth",
        "relation",
        "twitterIds",
        "facebookIds",
        "linkedInIds",
        "telegramIds",
        "basic",
        "socialize",
        "media",
        "customFields",
      ];
      // 组装basic对象
      const basicObj = {};
      const socialMediaFields = [
        "twitterIds",
        "facebookIds",
        "linkedInIds",
        "telegramIds",
        "phoneNumbers",
      ];
      standardFields.forEach((field) => {
        if (field === "basic") return;
        if (socialMediaFields.includes(field)) {
          // 保证为数组格式
          if (Array.isArray(p[field])) {
            basicObj[field] = p[field];
          } else if (typeof p[field] !== "undefined") {
            basicObj[field] = [p[field]];
          } else {
            basicObj[field] = [];
          }
        } else if (Array.isArray(p[field])) {
          basicObj[field] = p[field][0] || "";
        } else if (
          typeof p[field] === "string" ||
          typeof p[field] === "number"
        ) {
          basicObj[field] = p[field];
        }
      });
      if (p.basic && typeof p.basic === "object") {
        Object.assign(basicObj, p.basic);
      }
      // 组装customFields
      const customFields = {};
      Object.keys(p).forEach((key) => {
        if (!standardFields.includes(key)) {
          customFields[key] = Array.isArray(p[key]) ? p[key][0] : p[key];
        }
      });
      // 组装params
      data._source.params = {
        ...p,
        basic: basicObj,
        customFields,
      };
      this.selectPersonData = data;
      console.log("state.selectPerson", this.selectPersonData);

      // 遍历社交媒体数据，提取 name 和 email
      /* const socialPlatforms = ["telegram", "twitter", "facebook", "linkedin"];
      socialPlatforms.forEach((platform) => {
        this[platform] = []; // 初始化对应的 state 数组
        if (
          data._source.params.media &&
          data._source.params.media[platform] &&
          Array.isArray(data._source.params.media[platform])
        ) {
          data._source.params.media[platform].forEach((item) => {
            if (item.name) {
              this[platform].push(item.name);
            }
          });
        }
      }); */
      this.personName = data._source.params.basic.name;
      this.identity = data._source.params.basic.identity;
      this.phone = data._source.params.basic.phone;
      this.email = data._source.params.basic.email;
      this.initData(data);
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {},
            msg: {
              table: "key_person",
              type: "public",
              relation: data._id + ";key_person",
            },
          },
        ],
        (res) => {
          console.log("获取关系数据", res);
          let personRelationIds = [];
          if (res.length > 0) {
            res.forEach((item) => {
              // 遍历人员关系数据

              // 通过人员关系id进行搜索ES
              let add_es_query_conditions = {
                bool: {
                  must: [{ term: { _id: item.columnValues.d.id } }],
                },
              };
              window.main.$main_socket.sendData(
                "Api.Search.SearchList.Query",
                [
                  {
                    head: {
                      from: 0,
                      size: 200,
                    },
                    control: {
                      query_type: "public",
                      query_string: "",
                      add_es_query_conditions: add_es_query_conditions,
                    },
                    msg: {
                      data_range_index_name: "key_person",
                    },
                  },
                ],
                (restwo) => {
                  console.log("res-person-relation", restwo);
                  if (!restwo?.hits?.hits?.length) return;
                  restwo.hits.hits.forEach((itemtwo) => {
                    let personData = buildPersonData(itemtwo._source.params);
                    this.personalRelations.push({
                      name: personData.basic.name,
                      relationship: personData.relation,
                      age: personData.basic.age,
                      phone: personData.basic.phone,
                      identity: personData.basic.identity,
                      dateBirth: personData.basic.dateBirth,
                      remark: personData.basic.remark,
                      sex: personData.basic.sex,
                    });
                    console.log("personData", personData);
                    /* this.personRelationSearchList.push(personData); */
                  });
                }
              );
              // 目标人数据构造
              function buildPersonData(params) {
                console.log("buildPersonData", params);

                // 如果params已经是对象格式，直接返回
                if (typeof params === "object" && !Array.isArray(params)) {
                  return params;
                }

                // 处理params为数组的情况
                if (Array.isArray(params)) {
                  const paramsObj = {};
                  params.forEach((param) => {
                    paramsObj[param.k] = param.v;
                  });

                  // 标准字段列表
                  const standardFields = [
                    "type",
                    "avatar",
                    "name",
                    "remark",
                    "sex",
                    "age",
                    "phoneNumbers",
                    "identity",
                    "email",
                    "dateBirth",
                    "relation",
                    "twitterIds",
                    "facebookIds",
                    "linkedInIds",
                    "telegramIds",
                    "basic",
                    "socialize",
                    "media",
                    "customFields",
                  ];

                  // 组装basic对象
                  const basicObj = {
                    type: Array.isArray(paramsObj.type)
                      ? paramsObj.type[0]
                      : paramsObj.type || "key_person",
                    id: paramsObj.id
                      ? Array.isArray(paramsObj.id)
                        ? paramsObj.id[0]
                        : paramsObj.id
                      : "",
                    name: Array.isArray(paramsObj.name)
                      ? paramsObj.name[0]
                      : paramsObj.name || "",
                    remark: Array.isArray(paramsObj.remark)
                      ? paramsObj.remark[0]
                      : paramsObj.remark || "",
                    sex: Array.isArray(paramsObj.sex)
                      ? paramsObj.sex[0]
                      : paramsObj.sex || "",
                    age: paramsObj.age
                      ? parseInt(
                          Array.isArray(paramsObj.age)
                            ? paramsObj.age[0]
                            : paramsObj.age
                        )
                      : 0,
                    phone: Array.isArray(paramsObj.phoneNumbers)
                      ? paramsObj.phoneNumbers[0]
                      : paramsObj.phoneNumbers || "",
                    identity: Array.isArray(paramsObj.identity)
                      ? paramsObj.identity[0]
                      : paramsObj.identity || "",
                    email: Array.isArray(paramsObj.email)
                      ? paramsObj.email[0]
                      : paramsObj.email || "",
                    avatar: Array.isArray(paramsObj.avatar)
                      ? paramsObj.avatar[0]
                      : paramsObj.avatar || "",
                    dateBirth: Array.isArray(paramsObj.dateBirth)
                      ? paramsObj.dateBirth[0]
                      : paramsObj.dateBirth || "",
                  };

                  // 组装customFields
                  const customFields = {};
                  Object.keys(paramsObj).forEach((key) => {
                    if (!standardFields.includes(key)) {
                      customFields[key] = Array.isArray(paramsObj[key])
                        ? paramsObj[key][0]
                        : paramsObj[key];
                    }
                  });

                  // 组装media对象
                  const buildMedia = (p) => {
                    const platforms = [
                      { key: "telegram", idKey: "telegramIds" },
                      { key: "twitter", idKey: "twitterIds" },
                      { key: "facebook", idKey: "facebookIds" },
                      { key: "linkedin", idKey: "linkedInIds" },
                    ];
                    const media = {};
                    platforms.forEach(({ key, idKey }) => {
                      const arr = Array.isArray(p[idKey])
                        ? p[idKey]
                        : p[idKey]
                        ? [p[idKey]]
                        : [];
                      media[key] = arr.map((id) => ({
                        idNum: id,
                        name: id,
                      }));
                    });
                    return media;
                  };

                  // 处理关系数据（兼容旧数据格式）
                  let relationData = { person: [], organization: [] };
                  if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
                    paramsObj.relation.forEach((relationStr) => {
                      try {
                        const parsed = JSON.parse(relationStr);
                        if (parsed.person && Array.isArray(parsed.person)) {
                          relationData.person = [
                            ...relationData.person,
                            ...parsed.person,
                          ];
                        }
                        if (
                          parsed.organization &&
                          Array.isArray(parsed.organization)
                        ) {
                          relationData.organization = [
                            ...relationData.organization,
                            ...parsed.organization,
                          ];
                        }
                      } catch (e) {
                        console.warn("解析关系数据失败:", relationStr, e);
                      }
                    });
                  }

                  // 组装最终params对象
                  const result = {
                    ...paramsObj,
                    basic: basicObj,
                    customFields,
                    media: buildMedia(paramsObj),
                    relation: relationData,
                  };

                  console.log("构建后的人员数据:", result);
                  return result;
                }

                // 如果params格式不识别，返回原始数据
                console.warn("无法识别的params格式:", params);
                return params;
              }
            });
          }
        }
      );
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.Query",
        [
          {
            head: {},
            msg: {
              table: "key_person",
              type: "public",
              relation: data._id + ";key_organization",
            },
          },
        ],
        (res) => {
          console.log("获取组织关系数据", res);
          if (res.length > 0) {
            res.forEach((item) => {
              let add_es_query_conditions = {
                bool: {
                  must: [{ term: { _id: item.columnValues.d.id } }],
                },
              };
              window.main.$main_socket.sendData(
                "Api.Search.SearchList.Query",
                [
                  {
                    head: {
                      from: 0,
                      size: 200,
                    },
                    control: {
                      query_type: "public",
                      query_string: "",
                      add_es_query_conditions: add_es_query_conditions,
                    },
                    msg: {
                      data_range_index_name: "key_organization",
                    },
                  },
                ],
                (restwo) => {
                  console.log("res-organization-relation", restwo);
                  if (!restwo?.hits?.hits?.length) return;
                  restwo.hits.hits.forEach((itemtwo) => {
                    let organizationData = buildOrganizationData(
                      itemtwo._source.params
                    );
                    this.organizationRelations.push({
                      name: organizationData.basic.name,
                      relationship: organizationData.relation,
                      belong: organizationData.basic.belong,
                      createTime: organizationData.basic.createTime,
                      desi: organizationData.basic.desi,
                      remark: organizationData.basic.remark,
                    });
                  });
                }
              );
              // 目标组织数据构造
              function buildOrganizationData(params) {
                console.log("buildOrganizationData", params);

                // 如果params已经是对象格式，直接返回
                if (typeof params === "object" && !Array.isArray(params)) {
                  return params;
                }

                // 处理params为数组的情况
                if (Array.isArray(params)) {
                  const paramsObj = {};
                  params.forEach((param) => {
                    paramsObj[param.k] = param.v;
                  });

                  // 定义一个辅助函数来获取数组中的第一个元素或默认值
                  const getFirstOrDefault = (arr, defaultVal = "") => {
                    return Array.isArray(arr) && arr.length > 0
                      ? arr[0]
                      : defaultVal;
                  };

                  // 创建basic对象以兼容前端视图
                  const basicObj = {
                    id: getFirstOrDefault(paramsObj.id),
                    name: getFirstOrDefault(paramsObj.name),
                    remark: getFirstOrDefault(paramsObj.remark),
                    createTime: getFirstOrDefault(paramsObj.createTime),
                    belong: getFirstOrDefault(paramsObj.belong),
                    desi: getFirstOrDefault(paramsObj.desi),
                    avatar: getFirstOrDefault(paramsObj.avatar),
                  };

                  // 组装customFields
                  const standardFields = [
                    "id",
                    "name",
                    "remark",
                    "createTime",
                    "belong",
                    "desi",
                    "avatar",
                    "relation",
                    "customFields",
                  ];
                  const customFields = {};
                  Object.keys(paramsObj).forEach((key) => {
                    if (!standardFields.includes(key)) {
                      customFields[key] = getFirstOrDefault(paramsObj[key]);
                    }
                  });

                  // 处理关系数据
                  let relationData = { person: [], organization: [] };
                  if (paramsObj.relation && Array.isArray(paramsObj.relation)) {
                    paramsObj.relation.forEach((relationStr) => {
                      try {
                        const parsed = JSON.parse(relationStr);
                        if (parsed.person && Array.isArray(parsed.person)) {
                          relationData.person = [
                            ...relationData.person,
                            ...parsed.person,
                          ];
                        }
                        if (
                          parsed.organization &&
                          Array.isArray(parsed.organization)
                        ) {
                          relationData.organization = [
                            ...relationData.organization,
                            ...parsed.organization,
                          ];
                        }
                      } catch (e) {
                        console.warn("解析组织关系数据失败:", relationStr, e);
                      }
                    });
                  }

                  // 组装最终params对象
                  const result = {
                    ...paramsObj,
                    basic: basicObj,
                    customFields,
                    relation: relationData,
                  };

                  console.log("构建后的组织数据:", result);
                  return result;
                }

                // 如果params格式不识别，返回原始数据
                console.warn("无法识别的组织params格式:", params);
                return params;
              }
            });
          }
        }
      );

      // 遍历人员关系数据

      //遍历组织关系数据
    },
    // 获取节点样式
    getNodeStyle(nodeName) {
      const targetNodes = {
        关注: {
          color: "#409EFF",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 50,
          lineHeight: 13,
        },
        粉丝: {
          color: "#67C23A",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 50,
          lineHeight: 13,
        },
        个人关系: {
          color: "#E6A23C",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 60,
          lineHeight: 13,
        },
        组织关系: {
          color: "#F56C6C",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 60,
          lineHeight: 13,
        },
        收信箱: {
          color: "#909399",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 50,
          lineHeight: 13,
        },
        邮箱: {
          color: "#7e57c2",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 50,
          lineHeight: 13,
        },
        发信箱: {
          color: "#606266",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 50,
          lineHeight: 13,
        },
        Telegram: {
          color: "#0088CC",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 60,
          lineHeight: 13,
        },
        LinkedIn: {
          color: "#0077B5",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 60,
          lineHeight: 13,
        },
        Facebook: {
          color: "#1877F2",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 60,
          lineHeight: 13,
        },
        Twitter: {
          color: "#1DA1F2",
          textColor: "#FFFFFF",
          fontSize: 10,
          symbolSize: 60,
          lineHeight: 13,
        },
      };

      // 查找匹配的节点类型
      for (const [key, style] of Object.entries(targetNodes)) {
        if (nodeName.includes(key)) {
          return {
            symbolSize: style.symbolSize,
            label: {
              show: true,
              position: "inside",
              color: style.textColor,
              fontSize: style.fontSize,
              fontWeight: "bold",
              lineHeight: style.lineHeight,
              formatter: function (params) {
                // 如果有 displayName 则使用，否则使用 name
                return params.data.displayName || params.data.name;
              },
            },
            itemStyle: {
              color: style.color,
              borderColor: "#fff",
              borderWidth: 2,
            },
          };
        }
      }

      // 默认样式
      return {
        label: {
          show: true,
          color: "#333333",
          fontSize: 10,
        },
      };
    },
    getChartData() {
      // 强制用局部变量，防止污染
      let nodes = [];
      let links = [];

      // 中心节点
      console.log("中心人物数据:", this.selectPersonData);
      nodes.push({
        id: "center",
        name: this.selectPersonData._source?.name || "未知用户",
        category: "center",
        symbolSize: 100,
        data: this.selectPersonData,
        itemStyle: {
          color: "#ff6b6b",
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: true,
          fontSize: 16,
          fontWeight: "bold",
          position: "inside",
          color: "#FFFFFF",
          lineHeight: 20,
          align: "center",
          formatter: function (params) {
            console.log("中心节点 formatter 被调用:", params);
            return params.data.name;
          },
        },
      });

      // 组织关系分组节点
      const orgArr = Array.isArray(this.organizationRelations)
        ? this.organizationRelations
        : this.organizationRelations
        ? Object.values(this.organizationRelations)
        : [];
      if (orgArr.length > 0) {
        nodes.push({
          id: "org-group",
          name: "组织关系",
          displayName: `组织关系\n${orgArr.length}`,
          count: orgArr.length,
          category: "org-group",
          symbolSize: 60,
          data: {},
          itemStyle: { color: "#45b7d1" },
        });
        links.push({
          source: "center",
          target: "org-group",
          name: "组织关系",
          lineStyle: { color: "#45b7d1", width: 2 },
        });
        for (const [index, org] of orgArr.entries()) {
          const nodeId = `org-${index}`;
          nodes.push({
            id: nodeId,
            name: org.name,
            category: "organization",
            symbolSize: 35,
            data: org,
            itemStyle: { color: "#45b7d1" },
          });
          links.push({
            source: "org-group",
            target: nodeId,
            name: org.relationship,
            lineStyle: { color: "#45b7d1", width: 2 },
          });
        }
      }
      console.log("personalRelations1155", this.personalRelations);
      // 个人关系分组节点
      const personalArr = Array.isArray(this.personalRelations)
        ? this.personalRelations
        : this.personalRelations
        ? Object.values(this.personalRelations)
        : [];
      console.log("personalArr1155", personalArr);
      if (personalArr.length > 0) {
        nodes.push({
          id: "personal-group",
          name: "个人关系",
          displayName: `个人关系\n${personalArr.length}`,
          count: personalArr.length,
          category: "personal-group",
          symbolSize: 60,
          data: {},
          itemStyle: { color: "#4ecdc4" },
        });
        links.push({
          source: "center",
          target: "personal-group",
          name: "个人关系",
          lineStyle: { color: "#4ecdc4", width: 2 },
        });
        for (const [index, person] of personalArr.entries()) {
          const nodeId = `personal-${index}`;
          nodes.push({
            id: nodeId,
            name: person.name,
            category: "personal",
            symbolSize: 35,
            data: person,
            itemStyle: { color: "#4ecdc4" },
          });
          links.push({
            source: "personal-group",
            target: nodeId,
            name: person.relationship || "个人关系",
            lineStyle: { color: "#4ecdc4", width: 2 },
          });
        }
      }

      // 应用样式到节点
      const styledNodes = nodes.map((node) => {
        // 跳过中心节点，保持其原有样式
        if (node.id === "center") {
          return node;
        }

        const nodeStyle = this.getNodeStyle(node.name);
        return {
          ...node,
          ...nodeStyle,
        };
      });
      return { nodes: styledNodes, links };
    },
    getAvatarSrc(avatar) {
      if (avatar && avatar.trim()) {
        return `/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${avatar}`;
      }
      return require("@/assets/images/user.png");
    },
  },
};
</script>

<style scoped lang="scss">
.topologyLay {
  margin-top: 15px;
  font-size: 12px;
  height: 300px;
  display: flex;
  border-bottom: 1px solid #e6e6e6;
  .topologyLay_left {
    width: 300px;
    height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    .topologyLay_left_avatar {
      background-color: #eee;
      display: flex;
      .custom-avatar {
        width: 64px;
        height: 100px;
        border-radius: 4px;
      }
      .avatar-info {
        margin-left: 10px;
        .info-row {
          width: 220px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          .info-value {
            color: #ccc;
            margin-left: 10px;
          }
        }
      }
    }
    .relation-collapse-content {
      overflow-y: auto;
    }
    .relation-row {
      line-height: 24px;
      .relation-value {
        color: #ccc;
        margin-left: 10px;
      }
    }
  }
  .topologyLay_right {
    width: 490px;
    height: 300px;
    position: relative;
    /* 遮罩层样式 - 只在图表容器内显示 */
    .loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 10;
      backdrop-filter: blur(4px);
      border-radius: 8px;
    }

    .loading-content {
      background: white;
      padding: 32px;
      border-radius: 12px;
      text-align: center;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      border: 1px solid #e2e8f0;
      min-width: 200px;
    }

    .loading-spinner {
      border: 3px solid #f3f4f6;
      border-top: 3px solid #4299e1;
      border-radius: 50%;
      width: 48px;
      height: 48px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }
      100% {
        transform: rotate(360deg);
      }
    }

    .loading-text {
      margin-top: 16px;
      font-size: 16px;
      color: #4a5568;
      font-weight: 500;
    }

    .loading-progress {
      margin-top: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .progress-bar {
      width: 200px;
      height: 20px;
      background-color: #e2e8f0;
      border-radius: 10px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #4299e1, #667eea);
      transition: width 0.1s ease-out;
      border-radius: 10px;
    }

    .progress-text {
      margin-left: 12px;
      font-size: 14px;
      font-weight: 600;
      color: #4a5568;
      min-width: 40px;
      text-align: left;
    }

    .loading-step {
      margin-top: 12px;
      font-size: 13px;
      color: #718096;
      font-style: italic;
    }

    .retry-button {
      padding: 8px 16px;
      background: #4299e1;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }

    .retry-button:hover {
      background: #3182ce;
    }
  }
}
</style>
