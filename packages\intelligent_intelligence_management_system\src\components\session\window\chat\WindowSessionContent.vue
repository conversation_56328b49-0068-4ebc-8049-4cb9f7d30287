<template>
  <div
    style="width: 100%"
    class="window-session-content w100"
    :class="{ user: role === 'user' }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <div
      class="info"
      v-if="
        (role === 'user' || role === 'tool' || role === 'assistant') &&
        itemData.content.trim().length > 0
      "
    >
      <el-image
        :src="
          role === 'user'
            ? require('/src/assets/images/user_img_default1.png')
            : require('/src/assets/images/img-chat-gpt.png')
        "
      >
        <div slot="error" class="image-slot">
          <img :src="require('/src/assets/images/img-chat-gpt.png')" alt="" />
        </div>
      </el-image>

      <span v-if="role === 'user'" style="color: #333">{{
        $store.state.userInfo.userinfo.username
      }}</span>
      <span
        v-if="role === 'tool' && itemData.content"
        @click="isShowFn"
        style="
          cursor: pointer;
          background-color: rgb(245, 245, 245);
          color: #333;
          border-radius: 10px;
          padding: 10px;
        "
        >工具调用<i
          style="margin-left: 5px; cursor: pointer"
          class="el-icon-arrow-down"
          v-if="role === 'tool' && itemData.content && isShow"
        ></i
        ><i
          style="margin-left: 5px; cursor: pointer"
          class="el-icon-arrow-up"
          v-if="role === 'tool' && itemData.content && !isShow"
        ></i
      ></span>
      <span v-if="role === 'assistant' && itemData.content.trim().length > 0"
        >Ai 助手</span
      >
    </div>

    <div v-if="isShow" class="content rounded-md">
      <div
        v-if="role != 'user' && itemData.content.trim().length > 0"
        style="
          background-color: rgb(245, 245, 245);
          padding: 20px 10px 10px 10px;
          border-radius: 10px;
        "
      >
        <div
          v-if="itemData && itemData.tool_calls.length > 0"
          v-for="(respItem, respIndex) in itemData.tool_calls"
          :key="respIndex"
        >
          <div v-if="role === 'tool'">
            <div v-if="respItem.function.name">
              <b>工具名称：{{ respItem.function.name }}</b>
            </div>
            <div v-if="respItem.function.arguments">
              查询条件：
              {{ respItem.function.arguments }}
            </div>
          </div>
        </div>
        <div v-if="role === 'tool' && itemData.content" style="width: 792px">
          工具响应:

          <json-viewer
            :value="convertToSafeJSON(itemData)"
            :expand-depth="0"
            copyable
            boxed
          ></json-viewer>
          <div
            v-if="itemData && itemData.tool_calls.length > 0"
            v-for="(respItem, respIndex) in itemData.tool_calls"
            :key="respIndex"
          >
            <div
              v-if="
                respItem.function.name === 'ES搜索工具' &&
                convertToSafeJSON(itemData).hasOwnProperty('resp') &&
                convertToSafeJSON(itemData).resp.hasOwnProperty('hits') &&
                convertToSafeJSON(itemData).resp.hits.hits.length > 0
              "
              style="max-height: 100px; overflow: auto; margin-top: 15px"
            >
              <div
                style="line-height: 24px; cursor: pointer; color: #4056ff"
                v-for="hitsItem in convertToSafeJSON(itemData).resp.hits.hits"
                :key="hitsItem['_id']"
                @click="NewsDisplay(hitsItem)"
              >
                • {{ hitsItem["_source"].content_article.substring(0, 20) }}
              </div>
            </div>
          </div>
        </div>
        <div
          class="markdown-container"
          :class="{ collapsed: isContentCollapsed }"
        >
          <div
            v-if="
              role !== 'tool' &&
              role !== 'user' &&
              itemData.content.includes('<div>') &&
              itemData.content.includes('</div>')
            "
            class="collapse-header"
            @click="toggleContent"
          >
            深度思考
            <span v-if="isContentCollapsed">
              <i class="el-icon-arrow-up"></i>
            </span>
            <span v-else>
              <i class="el-icon-arrow-down"></i>
            </span>
            <!-- <span>{{ isContentCollapsed ? '展开' : '收起' }}</span> -->
          </div>
          <MarkdownView
            ref="mv"
            v-if="
              contentShowType == ContentShowType.Markdown &&
              role !== 'tool' &&
              role !== 'user' &&
              itemData.content.trim().length > 0
            "
            :content="itemData.content"
          ></MarkdownView>
          <el-button
            v-if="
              !log &&
              contentShowType == ContentShowType.Markdown &&
              role !== 'tool' &&
              role !== 'user' &&
              itemData.content.trim().length > 0
            "
            size="mini"
            type="text"
            class="copy-btn"
            @click="copyWithVueClipboard(itemData.content)"
          >
            <i class="iconfont icon-fuzhi"></i>复制
          </el-button>
          <el-button
            v-if="
              !log &&
              contentShowType == ContentShowType.Markdown &&
              role !== 'tool' &&
              role !== 'user' &&
              itemData.content.trim().length > 0
            "
            size="mini"
            type="text"
            @click="downloadMarkdown"
            class="download-btn"
          >
            <i class="el-icon-download"></i>下载
          </el-button>

          <el-button
            v-if="
              !log &&
              contentShowType == ContentShowType.Markdown &&
              role !== 'tool' &&
              role !== 'user' &&
              itemData.content.trim().length > 0
            "
            size="mini"
            type="text"
            class="copy-btn-bottom"
            @click="copyWithVueClipboard(itemData.content)"
          >
            <i class="iconfont icon-fuzhi"></i>复制
          </el-button>
          <el-button
            v-if="
              !log &&
              contentShowType == ContentShowType.Markdown &&
              role !== 'tool' &&
              role !== 'user' &&
              itemData.content.trim().length > 0
            "
            size="mini"
            type="text"
            @click="downloadMarkdown"
            class="download-btn-bottom"
          >
            <i class="el-icon-download"></i>下载
          </el-button>
        </div>
        <div
          class="content-html"
          v-html="itemData.content"
          v-if="contentShowType == ContentShowType.Html"
        />
      </div>

      <div
        v-if="role === 'user' && itemData.content.length > 0 && !isEditing"
        style="
          position: relative;
          background-color: rgb(239, 246, 255);
          padding: 10px;
          border-radius: 4px;
        "
      >
        {{ itemData.content }}
        <el-button
          title="复制"
          size="mini"
          type="text"
          class="copy-btn-bottom-user"
          @click="copyWithVueClipboard(itemData.content)"
        >
          <i class="iconfont icon-fuzhi"></i>
        </el-button>
        <el-button
          v-if="!log"
          title="重新发送"
          size="mini"
          type="text"
          class="re-btn-bottom-user"
          @click="regenerateUser"
        >
          <i class="el-icon-refresh"></i>
        </el-button>
        <el-button
          v-if="!log"
          title="修改"
          size="mini"
          type="text"
          class="edit-btn-bottom-user"
          @click="startEdit"
        >
          <i class="el-icon-edit-outline"></i>
        </el-button>
      </div>
      <div
        v-if="role === 'user' && isEditing"
        style="
          background-color: rgb(239, 246, 255);
          padding: 10px;
          border-radius: 4px;
        "
      >
        <el-input type="textarea" autosize v-model="editedContent"></el-input>
        <div style="text-align: right; margin-top: 10px">
          <el-button type="primary" size="mini" @click="saveEdit"
            >确定</el-button
          >
          <el-button size="mini" @click="cancelEdit">取消</el-button>
        </div>
      </div>
      <div
        v-if="
          !log &&
          (index + 1 === sessionRecordData.length ||
            sessionRecordData[index + 1].role === 'user')
        "
        @click="regenerate"
      >
        <el-button title="重新发送" size="medium" type="text">
          <i class="el-icon-refresh"></i>
        </el-button>
      </div>
    </div>
    <!--  拓扑图 开始-->
    <div
      v-if="
        contentShowType == ContentShowType.Markdown &&
        role === 'tool' &&
        itemData.hasOwnProperty('tool_calls') &&
        itemData.tool_calls.length > 0 &&
        itemData.tool_calls[0].function.name === 'Hbase搜索工具' &&
        JSON.parse(this.itemData.content).resp.result.length
      "
    >
      <div style="text-align: right">
        <el-button type="primary" @click="initEcharts()" size="mini"
          >重置拓扑图</el-button
        >
      </div>

      <div
        ref="echartRef"
        style="width: 100%; height: 400px; min-width: 800px"
      ></div>
    </div>

    <!--  拓扑图 结束-->
    <!--目标人和目标组织 开始-->
    <div
      v-if="
        contentShowType == ContentShowType.Markdown &&
        role === 'tool' &&
        itemData.hasOwnProperty('tool_calls') &&
        itemData.tool_calls.length > 0 &&
        itemData.tool_calls[0].function.name === 'es重点人和重点组织查询工具' &&
        JSON.parse(this.itemData.content).resp.hits.hasOwnProperty('hits')
      "
    >
      <!-- <div
        class="id-card"
        v-for="(item, index) in personList"
        :key="index"
        @click="clickCard(item)"
      > -->
      <div v-for="(item, index) in personList" :key="index">
        <!-- 重点人重点组织（卡片+拓扑图）组件 -->
        <topology :myId="'topology' + index" :myData="item"></topology>
        <!-- 身份证正面 -->
        <!-- <div class="id-card-front">
          <div class="id-card-content">
            <div class="id-card-photo">
              <el-avatar :size="120" @error="handleAvatarError">
                <img src="@/assets/images/user.png" style="width: 100%" />
              </el-avatar>
            </div>

            <div class="id-card-info">
              <div class="info-row">
                <span class="label" v-if="item['_index'] === 'key_person'"
                  >姓名:</span
                >
                <span class="label" v-if="item['_index'] === 'key_organization'"
                  >组织名称:</span
                >
                <span class="value">{{ item._source.params.name }}</span>
              </div>
              <div class="info-row remark">
                <span class="label">备注:</span>
                <span class="value">{{
                  item._source.params.remark || "暂无备注"
                }}</span>
              </div>
              <div class="info-row" v-if="item['_index'] === 'key_person'">
                <span class="label">性别:</span>
                <span class="value">{{ item._source.params.sex }}</span>
                <span class="label" style="margin-left: 10px">年龄:</span>
                <span class="value">{{ item._source.params.age }}</span>
              </div>
              <div
                class="info-row"
                v-if="item['_index'] === 'key_organization'"
              >
                <span class="label">所属:</span>
                <span class="value">{{ item._source.params.belong }}</span>
              </div>
              <div class="info-row" v-if="item['_index'] === 'key_person'">
                <span class="label">手机号:</span>
                <span class="value">{{ item._source.params.phone }}</span>
              </div>
              <div
                class="info-row"
                v-if="item['_index'] === 'key_organization'"
              >
                <span class="label">创建时间:</span>
                <span class="value">{{ item._source.params.createTime }}</span>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 卡片悬停效果 -->
        <!-- <div class="id-card-hover"></div> -->
      </div>
    </div>
    <!--目标人和目标组织 结束-->
    <!-- <chat-tools
      :content="itemData.content"
      :session-record-id="itemData.id"
      :session-id="itemData.sessionId"
      :create-time="itemData.createTime"
      @deleteAfter="handleDeleteAfter"
      v-show="toolsShow && openTools"
    ></chat-tools>
    <div style="height: 40px" v-show="!toolsShow && openTools"></div>
    <div style="height: 40px" v-show="!openTools"></div> -->
  </div>
</template>

<script>
import { mapState } from "vuex";
import * as echarts from "echarts";
import { Document, Packer, Paragraph, TextRun } from "docx";
function deepParseJson(data) {
  if (typeof data === "string") {
    try {
      const parsed = JSON.parse(data);
      return deepParseJson(parsed); // 递归解析嵌套字符串‌:ml-citation{ref="5,7" data="citationList"}
    } catch (e) {
      return data; // 非 JSON 字符串则保留原值‌:ml-citation{ref="5" data="citationList"}
    }
  } else if (Array.isArray(data)) {
    return data.map((item) => deepParseJson(item));
  } else if (typeof data === "object" && data !== null) {
    Object.keys(data).forEach((key) => {
      data[key] = deepParseJson(data[key]);
    });
  }
  return data;
}
import ContentShowType from "@/common/constants/ContentShowType";
import MarkdownView from "@/components/MarkdownView/index";
import topology from "@/components/session/window/topology/topology.vue";
import { set } from "nprogress";
/* import ChatTools from "@/components/session/window/ChatTools"; */

export default {
  name: "WindowSessionContent",
  components: {
    /*  ChatTools, */
    MarkdownView,
    topology,
  },
  props: {
    log: { type: Boolean, default: false },
    index: { type: Number, required: true },
    titleName: { type: String, required: true },
    role: { type: String, required: true },
    itemData: { type: Object, required: true },
    contentShowType: { type: String, default: ContentShowType.Markdown },
    /*  openTools: { type: Boolean, default: true }, */
  },
  data() {
    return {
      isEditing: false,
      editedContent: "",
      isShow: true, // 已有的控制变量
      isContentCollapsed: false, // 新增的内容折叠控制变量
      ContentShowType,
      testData: null,
      html: "",
      /* toolsShow: false, */
    };
  },
  created() {},
  computed: {
    ...mapState({
      sessionRecordData: (state) => state.chat.sessionRecordData,
    }),
    personList: {
      get(val) {
        let data = JSON.parse(this.itemData.content).resp;
        console.log("444personList", data);
        let newdata;
        if (data?.hits?.hits?.length) {
          newdata = data.hits.hits.map((item) => {
            const newItem = { ...item };
            if (newItem._source && Array.isArray(newItem._source.params)) {
              // 转为对象结构
              const paramsObj = {};
              newItem._source.params.forEach((param) => {
                // id 直接赋值，其他尝试解析为对象
                if (param.k === "id") {
                  paramsObj.id = param.v;
                } else {
                  try {
                    paramsObj[param.k] = JSON.parse(param.v);
                  } catch (e) {
                    paramsObj[param.k] = param.v;
                  }
                }
              });
              newItem._source.params = paramsObj;
            }
            return newItem;
          });

          return newdata;
        } else {
          return [];
        }
      },
      set(nval) {},
    },
  },
  mounted() {
    // 使用setTimeout确保DOM完全渲染
    setTimeout(() => {
      this.initEcharts();
    }, 300);
    window.addEventListener("resize", this.handleResize);
  },
  methods: {
    NewsDisplay(val) {
      const routeData = this.$router.resolve({
        name: "NewsDisplay",
        query: {
          id: val.id ? val.id : val._id,
          index: val.index ? val.index : val._index,
        },
      });
      window.open(routeData.href, "_blank");
    },
    startEdit() {
      this.isEditing = true;
      this.editedContent = this.itemData.content;
    },
    saveEdit() {
      this.itemData.content = this.editedContent;
      this.$emit("regenerateUser", {
        myindex: this.index,
        mycontent: this.editedContent,
      });
      this.isEditing = false;
    },
    cancelEdit() {
      this.isEditing = false;
    },
    //点击user重新发送
    regenerateUser() {
      this.$emit("regenerateUser", {
        myindex: this.index,
        mycontent: this.itemData.content,
      });
    },
    //重新发送
    regenerate() {
      this.$emit("regenerate", this.index);
    },
    //复制
    copyWithVueClipboard(v) {
      this.$copyText(v).then(
        () => {
          this.$message({
            message: "复制成功",
            type: "success",
          });
        },
        () => {
          this.$message.error("复制失败");
        }
      );
    },
    // 删除methods中的mounted
    isShowFn() {
      this.isShow = !this.isShow;
    },
    toggleContent() {
      this.isContentCollapsed = !this.isContentCollapsed;
    },
    convertToSafeJSON(rawString) {
      console.log("rawString", rawString);
      if (rawString.content.is_error) {
        return { error: rawString.content };
      } else {
        try {
          // 尝试直接解析 JSON

          return JSON.parse(rawString.content);
        } catch (err) {
          // 解析失败时，返回包装后的错误 JSON
          return {
            result: rawString.content,
          };
        }
      }
    },
    handleMouseEnter() {
      this.toolsShow = true;
    },
    handleMouseLeave() {
      this.toolsShow = false;
    },
    handleDeleteAfter() {
      this.$emit("handleFlushThisSession", this.itemData.sessionId);
    },
    downloadMarkdown() {
      //把ai回复下载成markdown文档到电脑上
      /* if (this.itemData && this.itemData.content) {
        const content = this.itemData.content;
        const blob = new Blob([content], { type: "text/markdown" });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `markdown-${new Date().getTime()}.md`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } */
      if (this.itemData && this.itemData.content) {
        let html = "";
        let divCount = 0;
        const result = this.itemData.content.replace(/<\/?div>/g, (match) => {
          if (match === "<div>") {
            if (divCount === 0) {
              divCount++;
              return "头深度思考<<br />"; // 第一个 <div> 替换成加粗+换行
            }
          } else if (match === "</div>") {
            if (divCount === 1) {
              divCount++;
              return "<br />头回复内容<br />"; // 第一个 </div> 替换成换行+加粗
            }
          }
          return match; // 其他情况保持不变
        });
        html += result.replace(/\n/g, "<br>");
        console.log("itemData", this.itemData);
        this.html = html;
        this.docx(this.titleName);
      }
    },
    docx(title) {
      //生成段落 根据options进行配置
      const generateParagraph = (options) => {
        let {
          text = "",
          size = 24,
          margin = {
            left: 30,
            right: 30,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              font: {
                name: "宋体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      const generateParagraphHead = (options) => {
        let {
          text = "",
          bold = true,
          size = 26,
          margin = {
            left: 50,
            right: 50,
            top: 120,
            bottom: 120,
          },
          breakPage = false,
        } = options;
        let P = new Paragraph({
          children: [
            new TextRun({
              text,
              size,
              bold,
              font: {
                name: "黑体", // 只要是word中有的字体类型都可以生效
              },
            }),
          ],
          // 离左边距离 类似于margin-left
          indent: {
            left: margin?.left,
          },
          // 离上边和下边的距离 类似于margin-top/bottom
          spacing: {
            before: margin?.top,
            after: margin?.bottom,
          },
          // 是否在这段文字前加入分页符
          pageBreakBefore: breakPage,
        });
        return P;
      };
      let test = this.html.replace(/<[^>]*>/g, "<br>").split("<br>");
      let paragraphList = test.map((e) => {
        // 移除所有Markdown标记
        const cleanedText = e
          .replace(/!\x5b[^\]]*\x5d\x28[^\)]+\)/g, "") // 移除图片
          .replace(/\x5b([^\]]+)\x5d\x28[^\)]+\)/g, "$1") // 移除链接，但保留文本
          .replace(/^(#+)\s/g, "") // 移除标题标记
          .replace(/^>\s/g, "") // 移除块引用
          .replace(/^\s*([-*+]|\d+\.)\s+/g, "") // 移除列表标记
          .replace(/(\*\*|__|_|\*|~~|`)/g, ""); // 移除粗体、斜体、删除线和代码标记
        if (cleanedText.slice(0, 1) == "头") {
          let opt = {
            text: cleanedText.slice(1),
          };
          return generateParagraphHead(opt);
        } else {
          let opt = {
            text: cleanedText,
          };
          return generateParagraph(opt);
        }
      });
      //按照数据填充生成文档 内容放置于sections
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: paragraphList,
          },
        ],
      });

      //保存导出为word
      Packer.toBlob(doc).then((blob) => {
        saveAs(
          blob,
          title
            ? new Date().toLocaleDateString("zh-CN") + title + "的舆情简报.docx"
            : new Date().toLocaleDateString("zh-CN") + "舆情简报.docx"
        );
      });
      this.checkAll = false;
    },
    initEcharts() {
      console.log("Hbase搜索工具", this.itemData);
      // 确保DOM元素存在
      if (!this.$refs.echartRef) {
        console.error("ECharts container not found");
        return;
      }

      // 确保有数据
      if (
        !this.itemData.hasOwnProperty("tool_calls") ||
        this.itemData.content.length === 0 ||
        this.itemData.tool_calls[0].function.name != "Hbase搜索工具"
      ) {
        console.error("No data available for chart");
        return;
      }

      if (
        this.itemData.hasOwnProperty("tool_calls") &&
        this.itemData.tool_calls.length > 0 &&
        this.itemData.tool_calls[0].function.name === "Hbase搜索工具" &&
        this.itemData.content.length > 0 &&
        JSON.parse(this.itemData.content).resp.result.length > 0
      ) {
        let testDataList;
        testDataList = JSON.parse(this.itemData.content).resp.result;

        // 销毁已存在的实例
        const existingChart = echarts.getInstanceByDom(this.$refs.echartRef);
        if (existingChart) {
          existingChart.dispose();
        }

        const chartDom = this.$refs.echartRef;
        const myChart = echarts.init(chartDom);

        // 处理数据创建节点和连接
        const nodes = [];
        const links = [];
        const nodeMap = new Map();

        // 首先处理包含 "r" 的元素
        console.log("testDataList[0]", testDataList[0]);
        const rElement = testDataList.find((item) => item.columnValues.r);
        if (rElement) {
          const rowParts = rElement.row.split(";");
          const sourceId = rowParts[rowParts.length - 1]; // 获取最后一个分号后的值作为源节点ID

          // 添加源节点
          const sourceNode = {
            id: sourceId,
            name: sourceId,
            symbolSize: 20,
            category: 0,
            draggable: true,
            value: "中心用户",
            itemStyle: {
              color: "#FF6B6B",
              borderColor: "#FF6B6B",
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: "rgba(255, 107, 107, 0.5)",
            },
            label: {
              show: true,
              position: "right",
              fontSize: 12,
              fontWeight: "bold",
            },
          };
          nodes.push(sourceNode);
          nodeMap.set(sourceId, sourceNode);

          // 处理包含 "d" 的元素
          const dElements = testDataList.filter((item) => item.columnValues.d);
          dElements.forEach((item) => {
            const rowParts = item.row.split(";");
            const targetId = rowParts[rowParts.length - 1]; // 倒数第一个元素作为目标节点ID
            const relationType = rowParts[rowParts.length - 2]; // 倒数第二个元素作为关系类型
            const userData = item.columnValues.d;

            // 添加目标节点
            if (!nodeMap.has(targetId)) {
              const targetNode = {
                id: targetId,
                name: userData.nickname?.nickname || targetId,
                symbolSize: 16,
                category: 1,
                draggable: false,
                value: userData.followers_count || "0",
                itemStyle: {
                  color: "#4ECDC4",
                  borderColor: "#4ECDC4",
                  borderWidth: 1,
                },
                label: {
                  show: true,
                  position: "right",
                  fontSize: 12,
                },
              };
              nodes.push(targetNode);
              nodeMap.set(targetId, targetNode);
            }

            // 添加连接关系
            links.push({
              source: sourceId,
              target: targetId,
              value: relationType,
              symbolSize: 8,
              lineStyle: {
                width: 2,
                curveness: 0.2,
                color: "#6C5B7B",
              },
              label: {
                show: true,
                formatter: relationType,
                fontSize: 12,
                color: "#666",
                distance: 15,
              },
            });
          });
        }

        console.log("生成的节点:", nodes);
        console.log("生成的连接:", links);

        const option = {
          title: {
            text: "拓补图",
            top: "top",
            left: "center",
          },
          tooltip: {
            trigger: "item",
            formatter: function (params) {
              if (params.dataType === "node") {
                return `${params.data.name}<br/>用户ID: ${params.data.id}`;
              }
              return `${params.data.source} → ${params.data.target}<br/>关系: ${params.data.value}`;
            },
          },
          legend: {
            data: ["中心用户", "关联用户"],
            top: "bottom",
          },
          animationDurationUpdate: 1500,
          animationEasingUpdate: "quinticInOut",
          series: [
            {
              type: "graph",
              layout: "force",
              data: nodes,
              links: links,
              categories: [{ name: "中心用户" }, { name: "关联用户" }],
              roam: {
                // 控制拖拽和缩放
                scaleLimit: {
                  min: 0.5,
                  max: 2,
                },
                // 或者限制平移范围
                // 需要配合grid或其他容器配置
              },
              draggable: true,
              label: {
                show: true,
                position: "right",
                formatter: "{b}",
              },
              force: {
                repulsion: 100,
                edgeLength: 100,
                gravity: 0.05,
                friction: 0.1,
                layoutAnimation: true,
              },
              emphasis: {
                scale: true,
                lineStyle: {
                  width: 4,
                },
              },
            },
          ],
        };

        try {
          myChart.setOption(option);
          console.log("ECharts initialized successfully");

          // 添加延时resize确保渲染
          setTimeout(() => {
            myChart.resize();
          }, 100);
        } catch (error) {
          console.error("Error setting ECharts option:", error);
        }
      }
    },
    handleResize() {
      if (this.$refs.echartRef) {
        const chart = echarts.getInstanceByDom(this.$refs.echartRef);
        if (chart) {
          chart.resize();
        }
      }
    },
    clickCard(data) {
      let path;
      if (data["_index"] === "key_person") {
        let person = JSON.stringify(data);
        localStorage.setItem("intelligence_person", person);
        const routeData = this.$router.resolve({
          path: "/personDetails",
        });
        window.open(routeData.href, "_blank");
      }
      if (data["_index"] === "key_organization") {
        let organi = JSON.stringify(data);
        localStorage.setItem("intelligence_organi", organi);
        const routeData = this.$router.resolve({
          path: "/oriDetails",
        });
        window.open(routeData.href, "_blank");
      }
      /* const routeData = this.$router.resolve({
        path: path,
        query: {
          data: JSON.stringify(data),
        },
      });
      window.open(routeData.href, "_blank"); */
    },
    handleAvatarError() {
      return true; // 使用默认头像
    },
  },
  watch: {
    // 监听isShow变化，当显示时重新初始化图表
    /* isShow(newVal) {
      if (newVal) {
        this.$nextTick(() => {
          this.initEcharts();
        });
      }
    }, */
    // 添加对itemData的监听
    itemData: {
      handler(newVal) {
        console.log("itemData变化");
        if (
          newVal.tool_calls &&
          newVal.tool_calls.length > 0 &&
          newVal.tool_calls[0].function.name === "Hbase搜索工具"
        ) {
          try {
            // 将content字符串转换为JSON对象
            this.testData = JSON.parse(newVal.content);
            console.log("this.testData", this.testData);
            // 初始化图表
            this.$nextTick(() => {
              this.initEcharts();
            });
          } catch (error) {
            console.error("解析JSON失败:", error);
          }
        }
      },
      deep: true,
    },
  },
  beforeDestroy() {
    // 清理图表实例
    if (this.$refs.echartRef) {
      const chart = echarts.getInstanceByDom(this.$refs.echartRef);
      if (chart) {
        chart.dispose();
      }
    }
    // 移除resize事件监听
    window.removeEventListener("resize", this.handleResize);
  },
};
</script>

<style scoped lang="scss">
.window-session-content {
  padding: 0.8rem 10% 0 10%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  color: var(--font-color-default);

  &:hover {
    background-color: var(--session-window-item-hover-color);
  }
}
.window-session-content.user {
  align-items: flex-end;
}

.info {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-left: -50px;
  color: #4d6bfe;
  span {
    font-size: 0.8rem;
  }
}
::v-deep.window-session-content .info .el-image img {
  width: 34px;
  /* height: 34px;
  border-radius: 50%; */
  vertical-align: middle;
  margin-right: 10px;
  margin-left: 10px;
}

.content {
  margin-top: 0.5rem;
  background: var(--session-window-system-background);
  padding: 0.25rem;
}
.content-html {
  padding: 0.4rem;
  word-wrap: break-word;
  font-size: 15px;
  max-width: 60vw;
}
.window-session-content.user .content {
  background: var(--session-window-user-background);
}
.window-session-content.user .content-html {
  color: var(--font-color-default2) !important;
}

::v-deep.window-session-content.user .content .markdown-body {
  color: var(--font-color-default2) !important;
}

::v-deep .chat-tools {
  display: flex;
  justify-content: flex-start;
}
::v-deep.window-session-content.user .chat-tools {
  justify-content: flex-end;
}
.markdown-container {
  position: relative;
  width: 100%;
}
.collapse-header {
  margin-left: 8px;
  border-radius: 4px;
  width: 100px;
  cursor: pointer;
  padding: 5px;
  color: #2e2e2e;
  background: #e5e5e6;
  text-align: left;
}

.download-btn {
  position: absolute;
  top: -20px;
  right: -10px;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 10; // 添加较高的 z-index
  background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 5px 10px; // 增加内边距
  border-radius: 4px; // 添加圆角

  &:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 1); // 悬停时背景完全不透明
  }
}
.download-btn-bottom {
  position: absolute;
  bottom: 0px;
  left: 0px;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 10; // 添加较高的 z-index
  //background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 5px 10px; // 增加内边距
  border-radius: 4px; // 添加圆角

  &:hover {
    opacity: 1;
    //background-color: rgba(255, 255, 255, 1); // 悬停时背景完全不透明
  }
}
.copy-btn {
  position: absolute;
  top: -22px;
  right: 50px;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 10; // 添加较高的 z-index
  background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 5px 10px; // 增加内边距
  border-radius: 4px; // 添加圆角

  &:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 1); // 悬停时背景完全不透明
  }
}
.copy-btn-bottom {
  position: absolute;
  bottom: 0px;
  left: 60px;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 10; // 添加较高的 z-index
  //background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 5px 10px; // 增加内边距
  border-radius: 4px; // 添加圆角

  &:hover {
    opacity: 1;
    //background-color: rgba(255, 255, 255, 1); // 悬停时背景完全不透明
  }
}
.copy-btn-bottom-user {
  position: absolute;
  bottom: -25px;
  right: -10px;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 10; // 添加较高的 z-index
  //background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 5px 10px; // 增加内边距
  border-radius: 4px; // 添加圆角

  &:hover {
    opacity: 1;
    //background-color: rgba(255, 255, 255, 1); // 悬停时背景完全不透明
  }
}
.re-btn-bottom-user {
  position: absolute;
  bottom: -25px;
  right: 20px;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 10; // 添加较高的 z-index
  //background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 5px 10px; // 增加内边距
  border-radius: 4px; // 添加圆角

  &:hover {
    opacity: 1;
    //background-color: rgba(255, 255, 255, 1); // 悬停时背景完全不透明
  }
}
.edit-btn-bottom-user {
  position: absolute;
  bottom: -25px;
  right: 50px;
  opacity: 0.7;
  transition: opacity 0.3s;
  z-index: 10; // 添加较高的 z-index
  //background-color: rgba(255, 255, 255, 0.9); // 添加半透明背景
  padding: 5px 10px; // 增加内边距
  border-radius: 4px; // 添加圆角

  &:hover {
    opacity: 1;
    //background-color: rgba(255, 255, 255, 1); // 悬停时背景完全不透明
  }
}
// 确保 MarkdownView 不会遮挡按钮
::v-deep .markdown-body {
  position: relative;
  z-index: 1;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

::v-deep .markdown-body p {
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
}

::v-deep .markdown-body pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}

::v-deep .markdown-body code {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.echarts-container {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  padding: 20px;
}
.container {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 20px;
  padding: 8px;
  overflow-y: auto;
  max-height: calc(100vh - 180px);
  .id-card {
    position: relative;
    width: 100%;
    height: 170px;
    perspective: 1000px;
    cursor: pointer;

    .id-card-front {
      position: relative;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 20px;
      transition: all 0.3s ease;
      border: 1px solid #e8e8e8;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: linear-gradient(90deg, #1890ff, #36cfc9);
      }

      .id-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .id-card-title {
          font-size: 18px;
          font-weight: bold;
          color: #1890ff;
          letter-spacing: 1px;
        }

        .id-card-type {
          font-size: 14px;
          color: #666;
          background: #e6f7ff;
          padding: 2px 8px;
          border-radius: 4px;
        }
      }

      .id-card-content {
        display: flex;
        gap: 20px;

        .id-card-photo {
          width: 100px;
          height: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
          :deep(.el-avatar) {
            width: 100% !important;
            height: 100% !important;
            border-radius: 12px !important;
            object-fit: cover;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
          }
        }

        .id-card-info {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .info-row {
            display: flex;
            margin-bottom: 8px;
            height: 25px;
            line-height: 25px;
            .label {
              width: auto;
              color: #666;
              font-size: 14px;
            }

            .value {
              margin-left: 5px;
              flex: 1;
              color: #333;
              font-size: 14px;
              font-weight: 500;
            }

            &.remark {
              .value {
                color: #666;
                font-size: 13px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
              }
            }
          }
        }
      }

      .id-card-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 12px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(0, 0, 0, 0.02);
        border-top: 1px dashed #e8e8e8;

        .id-card-qrcode {
          color: #1890ff;
          font-size: 20px;
        }

        .id-card-number {
          font-size: 12px;
          color: #999;
          font-family: monospace;
        }
      }
    }

    .id-card-hover {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      // background: rgba(24, 143, 255, 0.24);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: white;
      opacity: 0;
      transition: opacity 0.3s;
      border-radius: 12px;

      i {
        font-size: 32px;
        margin-bottom: 8px;
      }

      span {
        font-size: 16px;
        font-weight: 500;
      }
    }

    &:hover {
      .id-card-front {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
      }

      .id-card-hover {
        opacity: 1;
      }
    }
  }

  .empty-state {
    grid-column: 1 / -1;
    margin-top: 120px;
  }
}
</style>
