<template>
  <div style="position: relative">
    <Spin size="large" fix v-if="pathTreeLoad"></Spin>
    <Spin size="large" fix v-if="searchPrefixLoad || searchLastLoad">
      <Icon type="ios-loading" size="20" class="demo-spin-icon-load"></Icon>
      <div>前缀搜索路径：{{ searchData.pathArr[prefixSearchPathIndex] }}</div>
      <div>内容搜索路径：{{ searchData.pathArr[dataSearchPathIndex] }}</div>
      <Button
        type="error"
        size="small"
        style="margin-top: 10px"
        @click="handleStopSearch"
        >停止搜索</Button
      >
    </Spin>
    <!-- 搜索头部 -->
    <div class="search_header">
      <div class="his_path">
        <b>搜索路径:</b>
        <div class="path_list">
          <div v-for="(path, index) in historicalPathArr" :key="`path-${index}`">
            <Tag
              @click.native="historicalPathClick(path)"
              :class="
                path === nowCheckPath ? 'active_path path_item' : 'path_item'
              "
              type="dot"
              color="primary"
              :title="path"
              >{{ path }}</Tag
            >
            <Icon
              v-if="path !== historicalPathArr[historicalPathArr.length - 1]"
              type="ios-arrow-forward"
            />
          </div>
          <Input
            v-if="showAddHisPath"
            v-model="addhisPathValue"
            ref="inputHisPathRef"
            style="width: 70px"
            @on-blur="addHisPath"
            @keydown.enter="addHisPath"
          />
          <Button
            v-else
            style="width: 70px"
            type="info"
            ghost
            icon="md-add"
            @click="addHistoricalPath"
          />
        </div>
      </div>
    </div>
    <!-- 搜索内容 -->
    <div class="search_body">
      <div class="search_criteria">
        <div class="search_path">
          <b class="search_data_type_title">数据类型:</b>
          <vueTreeselect
            class="tree_select"
            v-model="selectPathValue"
            placeholder="请选择路径, 默认搜索全部路径"
            :multiple="true"
            :clearable="true"
            :default-expand-level="5"
            value-consists-of="ALL"
            :options="allPathTreeData"
            :close-on-select="false"
            :appendToBody="true"
            noOptionsText="暂无数据"
            noChildrenText="暂无子选项"
            @input="selectChangePath"
          />
          <Button
            type="info"
            style="margin-left: 10px"
            icon="md-refresh"
            @click="refreshPath"
            title="重新请求数据类型"
          ></Button>
        </div>
      </div>
      <div class="social_search_result_box" v-show="isShowResult">
        <div class="social_prefix_search">
          <div class="social_prefix_title">
            <b>前缀搜索</b>
          </div>
          <div
            class="social_prefix_list"
            @scroll="prefixListScroll"
            v-if="prefixSearchList.length"
          >
            <Card
              class="social_prefix_card"
              v-for="item in prefixSearchList"
              :key="item.row"
              @mouseenter.native="showTooltip($event, item)"
              @mouseleave.native="hideTooltip"
            >
              <p :title="item.columnValues.title">
                <b>数据类型：</b>{{ allPathDesc[item.columnValues.path] }}
              </p>
              <p :title="item.columnValues.title">
                <b>命中字段：</b
                ><span class="social_card_text"
                  >“{{ item.columnValues.title }}”</span
                >
              </p>
            </Card>
            <p class="social_search_over_tip" v-if="prefixSeachOver">
              数据已经到底了!
            </p>
          </div>
          <div class="social_no_prefix" v-else>
            <p>未查询到前缀数据。</p>
          </div>
          <!-- 鼠标悬浮提示框 -->
          <div
            class="social_prefix_type_tip"
            v-if="showTooltipMode"
            :style="'top:' + setTop + 'px'"
            @mouseenter="showTooltip(null, null)"
            @mouseleave="hideTooltip"
          >
            <div
              class="social_precise_card"
              v-for="(value, key) in prefixTypeList.columnValues.r"
              @click="preciseCardClick(key, prefixTypeList.columnValues)"
              :key="key"
            >
              <div class="social_card_body">
                <img
                  class="social_precise_img"
                  v-if="value.icon"
                  :src="`/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${value.icon}`"
                />
                <img
                  class="social_precise_img"
                  v-else
                  src="../../../assets/images/datatype.jpg"
                />
                <div class="social_precise_type">
                  <p :title="key"><b>字段类型: </b>{{ key }}</p>
                  <p v-if="value.nickname" :title="value.nickname">
                    <b>名称: </b>{{ value.nickname }}
                  </p>
                  <p v-if="value.detail" :title="value.detail">
                    <b>描述: </b>{{ value.detail }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 精确搜索 -->
        <div class="social_precise_search">
          <div class="social_precise_title">
            <b>精确搜索</b>
          </div>
          <div
            class="social_precise_list"
            v-if="
              preciseSearchList.length > 0 &&
              preciseSearchList[0].columnValues.r
            "
          >
            <template v-for="item in preciseSearchList">
              <div
                class="social_precise_card"
                v-for="(value, key) in item.columnValues.r"
                @click="preciseCardClick(key, item.columnValues)"
                :key="item.row + '-' + key"
              >
                <div class="social_card_body">
                  <img
                    class="social_precise_img"
                    v-if="value.icon"
                    :src="`/filesystem/api/rest/v2/node-0/small_file/get_sha512_file/icon/${value.icon}`"
                  />
                  <img
                    class="social_precise_img"
                    v-else
                    src="../../../assets/images/datatype.jpg"
                  />
                  <div class="social_precise_type">
                    <p :title="key"><b>字段类型: </b>{{ key }}</p>
                    <p v-if="value.nickname" :title="value.nickname">
                      <b>名称: </b>{{ value.nickname }}
                    </p>
                    <p v-if="value.detail" :title="value.detail">
                      <b>描述: </b>{{ value.detail }}
                    </p>
                    <p>
                      <b>数据类型: </b>{{ allPathDesc[item.columnValues.path] }}
                    </p>
                    <p :title="item.columnValues.title">
                      <b>关键字: </b>"{{ item.columnValues.title }}"
                    </p>
                  </div>
                </div>
              </div>
            </template>
            <p class="social_search_over_tip">数据已经到底了!</p>
          </div>
          <div class="no_precise" v-else>
            <p>未查询到精确类型。</p>
          </div>
        </div>
        <!-- 内容搜索 -->
        <div class="social_data_search">
          <div class="social_data_title">
            <b>内容搜索</b>
          </div>
          <div
            class="social_data_list"
            v-if="dataSearchList.length"
            @scroll="contentListScroll"
          >
            <div class="social_data_select_collect_box">
              <el-checkbox
                :indeterminate="isIndeterminate"
                v-model="checkAll"
                @change="handleCheckAllChange"
                >全选</el-checkbox
              >
              <el-button size="mini" plain @click="handleCollect"
                >收藏</el-button
              >
            </div>
            <div
              v-masonry
              item-selector=".item"
              horizontal-order="true"
              class="social_data_item"
            >
              <el-checkbox-group
                v-model="selectSocialData"
                @change="handleCheckSocialChange"
                style="display: flex; flex-wrap: wrap"
              >
                <div
                  v-masonry-tile
                  v-for="(item, index) in dataSearchList"
                  :key="'checbox-' + index"
                  class="social_item"
                >
                  <el-checkbox
                    :label="item"
                    style="width: 10%"
                    ><br
                  /></el-checkbox>
                  <template v-if="item" style="font-size: 14px">
                    <Content
                      :itemData="item"
                      @last-data-click="laseDataClick"
                    ></Content>
                  </template>
                </div>
              </el-checkbox-group>
            </div>
            <p class="social_search_over" v-if="dataSearchOver">
              数据已经到底了!
            </p>
          </div>
          <div class="social_no_data" v-else>
            <p>未查询到数据。</p>
          </div>
        </div>
      </div>
    </div>
    <!-- 收藏目标对话框 -->
    <el-dialog
      title="选取目录"
      top="10px"
      :visible.sync="collectDialog"
      width="40%"
      append-to-body
    >
      <div style="width: 95%">
        <collectTree
          :listType="'username'"
          :tableName="'favorites_data'"
          :getCollect="getCollect"
        ></collectTree>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapActions, mapState } from "vuex";
import Content from "./content-view.vue";
import vueTreeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import collectTree from "../collectTree.vue";

export default {
  name: "SocialEnginneringDatabase",
  components: {
    Content,
    vueTreeselect,
    collectTree,
  },
  data() {
    return {
      hisValueData:
        JSON.parse(
          localStorage.getItem(window.location.host + "hisValueData")
        ) || [],
      timer: null,
      selectDatabaseValue: "公共权限",
      currentDatabaseValue: "公共权限",
      selectCaseNameValue: [],
      currentCaseNameValue: [],
      firstSearchObj: {
        searchValue: "",
        selectDatabaseValue: "",
        selectPathValue: [],
      },
      selectPathValue: [],
      currentPathValue: [],
      searchValue: "",
      isShowResult: false,
      popstateTimeout: null,
      showTooltipMode: false,
      setTop: 0,
      setLeft: 0,
      tooltipCardElement: null,
      prefixTypeList: null,
      nowCheckPath: "/",
      historicalPathArr: ["/"],
      historicalPathObj: { "/": {} },
      showAddHisPath: false,
      addhisPathValue: "",
      showHisDom: false,
      isMouseOver: false,
      isFoucs: false,
      selectSocialData: [],
      collectDialog: false,
      isIndeterminate: true,
      checkAll: false,
      collectNum: 0,
      isStopSearch: false,
      isProgrammaticPathChange: false,
      tooltipTimer: null
    };
  },
  computed: {
    ...mapState({
      dataHaveLastObj: (state) =>
        state.socialEnginneringDatabase.dataHaveLastObj,
      dataSearchOver: (state) => state.socialEnginneringDatabase.dataSearchOver,
      prefixSeachOver: (state) =>
        state.socialEnginneringDatabase.prefixSeachOver,
      dataSearchList: (state) => state.socialEnginneringDatabase.dataSearchList,
      allPathDesc: (state) => state.socialEnginneringDatabase.allPathDesc,
      preciseSearchList: (state) =>
        state.socialEnginneringDatabase.preciseSearchList,
      prefixSearchList: (state) =>
        state.socialEnginneringDatabase.prefixSearchList,
      pathNumObj: (state) => state.socialEnginneringDatabase.pathNumObj,
      allPathTreeData: (state) =>
        state.socialEnginneringDatabase.allPathTreeData,
      selectCaseData: (state) => state.socialEnginneringDatabase.selectCaseData,
      searchData: (state) => state.socialEnginneringDatabase.searchData,
      prefixSearchPathIndex: (state) =>
        state.socialEnginneringDatabase.prefixSearchPathIndex,
      dataSearchPathIndex: (state) =>
        state.socialEnginneringDatabase.dataSearchPathIndex,
      caseTreeLoad: (state) => state.socialEnginneringDatabase.caseTreeLoad,
      searchPrefixLoad: (state) =>
        state.socialEnginneringDatabase.searchPrefixLoad,
      pathTreeLoad: (state) => state.socialEnginneringDatabase.pathTreeLoad,
      searchLastLoad: (state) => state.socialEnginneringDatabase.searchLastLoad,
      conditionsData: (state) => state.search.conditions.conditionsData,
    }),
  },
  created() {
    this.initialize();
  },
  mounted() {
    // this.getPathNum()
    // 添加popstate事件监听
    window.addEventListener("popstate", this.handlePopState);
    // 添加storage事件监听
    window.addEventListener("storage", this.handleStorage);
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }

    // 移除事件监听
    window.removeEventListener("popstate", this.handlePopState);
    window.removeEventListener("storage", this.handleStorage);
  },
  methods: {
    //使用当前时间戳与随机数结合当作预警词的唯一id
    reduceNumber() {
      let soleValue = Math.round(new Date().getTime() / 1000).toString();
      let random = new Array(
        "a",
        "b",
        "c",
        "d",
        "e",
        "f",
        "g",
        "h",
        "i",
        "j",
        "k",
        "l",
        "m",
        "n"
      );
      for (let i = 0; i < 6; i++) {
        let index = Math.floor(Math.random() * 13);
        soleValue += random[index];
      }
      return soleValue;
    },

    // 进行收藏
    getCollect(data) {
      this.$confirm("确定选择此目录收藏?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.selectSocialData.forEach((element) => {
            let prefix =
              1e13 -
              Math.round(new Date().getTime() / 1000) +
              data.id +
              this.reduceNumber();
            window.main.$main_socket.sendData(
              "Api.Search.SearchPrefixTable.AddData",
              [
                {
                  msg: {
                    type: "username",
                    authority:
                      window.main.$store.state.userInfo.userinfo.authority,
                    username:
                      window.main.$store.state.userInfo.userinfo.username,
                    table: "favorites_data",
                    prefix,
                    relation: data.id + ";social_work_library",
                    data: {
                      data: {
                        file_data: element,
                      },
                    },
                  },
                },
              ],
              (res) => {
                if (res.status === "ok") {
                  this.collectNum++;
                  if (this.collectNum === this.selectSocialData.length) {
                    this.$message.success("收藏成功!");
                    this.collectNum = 0;
                    this.selectSocialData = [];
                    this.collectDialog = false;
                  }
                }
              }
            );
          });
        })
        .catch((err) => {
          this.$message({
            message: "取消收藏",
            type: "info",
          });
        });
    },

    // 单选事件
    handleCheckSocialChange(val) {
      let checkedCount = val.length;
      this.checkAll = checkedCount === this.dataSearchList.length;
      this.isIndeterminate =
        checkedCount > 0 && checkedCount < this.dataSearchList.length;
    },

    // 全选事件
    handleCheckAllChange(value) {
      this.selectSocialData = value ? this.dataSearchList : [];
      console.log(
        "Check All Change:",
        this.selectSocialData,
        value,
        this.dataSearchList
      );
      this.isIndeterminate = false;
    },

    // 点击收藏按钮，判断是否选中数据，并弹出收藏对话框
    handleCollect() {
      if (this.selectSocialData.length > 0) {
        this.$confirm("是否确认收藏所选数据?", "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.collectDialog = true;
          })
          .catch(() => {
            this.$message.info("已取消收藏");
          });
      } else {
        this.$message.info("请选需要收藏的数据");
      }
    },

    // 页面初始化
    initialize() {
      console.log("initialize");
      this.hisValueData =
        JSON.parse(
          localStorage.getItem(window.location.host + "hisValueData")
        ) || [];
      this.$store.commit("socialEnginneringDatabase/SET_PATH_TREE_LOAD", true);
      // 获取路径树数据
      const selectPathData = localStorage.getItem(
        window.location.host + "selectPathData"
      );
      this.$store.commit("socialEnginneringDatabase/clearPathTreeData");
      console.log("selectPathData:", selectPathData);
      const allPathArr = localStorage.getItem(
        window.location.host + "allPathArr"
      );
      const allPathDesc = localStorage.getItem(
        window.location.host + "allPathDesc"
      );
      if (allPathArr) {
        this.$store.commit(
          "socialEnginneringDatabase/SET_ALL_PATH_ARR",
          JSON.parse(allPathArr)
        );
      }
      if (allPathDesc) {
        this.$store.commit(
          "socialEnginneringDatabase/SET_ALL_PATH_DESC",
          JSON.parse(allPathDesc)
        );
      }
      if (selectPathData) {
        console.log("进来了");
        const parsedData = JSON.parse(selectPathData);

        // 对恢复的数据进行去重处理
        const deduplicateTree = (nodes) => {
          if (!Array.isArray(nodes)) return nodes;

          const seenPaths = new Set();
          const deduplicated = [];

          for (const node of nodes) {
            if (!seenPaths.has(node.value || node.id)) {
              seenPaths.add(node.value || node.id);
              if (node.children && node.children.length > 0) {
                node.children = deduplicateTree(node.children);
              }
              deduplicated.push(node);
            }
          }

          return deduplicated;
        };

        const deduplicatedData = deduplicateTree(parsedData);
        this.$store.commit(
          "socialEnginneringDatabase/SET_ALL_PATH_TREE_DATA",
          deduplicatedData
        );
        this.$store.commit(
          "socialEnginneringDatabase/SET_PATH_TREE_LOAD",
          false
        );
        // 只有本地有缓存时，立即 searchClick
        this.searchClick();
      } else {
        // 本地无缓存，拉取完路径树后再 searchClick
        this.$store.dispatch("socialEnginneringDatabase/getPathTreeData", () => {
          this.searchClick();
        });
      }
      // 注意：searchClick 只在上面合适的时机调用，这里不再重复调用
      // this.searchClick();
      // 获取案件数据
      // const selectCaseData = localStorage.getItem('selectCaseData')
      // this.$store.commit('socialEnginneringDatabase/clearCaseTreeData')
      // if (selectCaseData) {
      //   this.$store.commit('socialEnginneringDatabase/SET_SELECT_CASE_DATA', JSON.parse(selectCaseData))
      //   this.$store.commit('socialEnginneringDatabase/SET_CASE_TREE_LOAD', false)
      // } else {
      //   this.$store.dispatch('socialEnginneringDatabase/getCaseTreeData')
      // }
    },

    // 刷新案件
    refreshCase() {
      this.$store.commit("socialEnginneringDatabase/SET_CASE_TREE_LOAD", true);
      this.$store.commit("socialEnginneringDatabase/clearCaseTreeData");
      this.$store.dispatch("socialEnginneringDatabase/getCaseTreeData");
    },

    // 刷新路径
    refreshPath() {
      this.$store.commit("socialEnginneringDatabase/SET_PATH_TREE_LOAD", true);
      this.$store.commit("socialEnginneringDatabase/clearPathTreeData");
      this.$store.dispatch("socialEnginneringDatabase/getPathTreeData");
    },

    // 请求统计数据
    getPathNum() {
      this.$store.dispatch("socialEnginneringDatabase/getAllPathDataNum");
      this.timer = setInterval(() => {
        this.$store.dispatch("socialEnginneringDatabase/getAllPathDataNum");
      }, 5000);
    },

    // 统计路径下的数据
    statisticPathNum() {
      this.$store.dispatch("socialEnginneringDatabase/sendStatisticsPath");
    },

    // 选择数据库
    changeRadioValue() {
      this.changClearHistoricalPath();
      this.selectCaseNameValue = [];
      this.currentCaseNameValue = [];
    },

    changeCaseNameValue() {
      this.changClearHistoricalPath();
    },

    // 选择路径变化
    selectChangePath(e) {
      console.log("selectChangePath:", e);
      if (!this.isProgrammaticPathChange) {
        this.changClearHistoricalPath();
      }
    },

    // 展开路径树
    unfoldPathTree(bool) {
      if (bool) {
        this.$store.dispatch("socialEnginneringDatabase/setPathDataNum");
      }
    },

    // 切换搜索条件时需要清空搜索路径
    changClearHistoricalPath() {
      if (this.historicalPathArr.length > 1) {
        this.$confirm("执行当前操作会清空搜索路径,是否继续执行?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.clearHistoricalPath();
            this.currentDatabaseValue = this.selectDatabaseValue;
            this.currentCaseNameValue = this.selectCaseNameValue;
            this.currentPathValue = this.selectPathValue;
          })
          .catch(() => {
            this.selectDatabaseValue = this.currentDatabaseValue;
            this.selectCaseNameValue = this.currentCaseNameValue;
            this.selectPathValue = this.currentPathValue;
          });
      } else {
        this.currentDatabaseValue = this.selectDatabaseValue;
        this.currentCaseNameValue = this.selectCaseNameValue;
        this.currentPathValue = this.selectPathValue;
      }
      console.log("changClearHistoricalPath:", this.selectPathValue);
    },

    // 搜索
    searchClick(type) {
      this.isStopSearch = false;
      this.$store.commit("socialEnginneringDatabase/SET_STOP_SEARCH", false);
      console.log("this.selectPathValue:", this.selectPathValue);

      // 防御：路径数据未加载时不发起请求
      const allPathArr = this.$store.state.socialEnginneringDatabase.allPathArr;
      console.log("allPathArr:", allPathArr);
      if (!allPathArr || allPathArr.length === 0) {
        this.$Message.warning("路径数据未加载完成，请稍后再试");
        return;
      }

      this.setHisSearchValue();
      let database = "";
      const caseId =
        this.selectCaseNameValue[this.selectCaseNameValue.length - 1];
      this.searchValue = this.conditionsData.queryString;
      switch (this.selectDatabaseValue) {
        case "公共权限":
          database = "public";
          break;
        case "部门权限":
          database = "authority";
          break;
        case "用户权限":
          database = "username";
          break;
        case "案件权限":
          database = "case";
          break;
      }
      const searchData = {
        value: this.searchValue,
        database: database,
        caseId: caseId,
        pathArr: this.selectPathValue,
        relationArr: this.historicalPathArr,
      };
      console.log("searchData:", searchData);
      this.$store.commit(
        "socialEnginneringDatabase/setPrefixSearchData",
        searchData
      );
      this.$store.commit(
        "socialEnginneringDatabase/SET_SEARCH_PREFIX_LOAD",
        true
      );
      this.$store.commit(
        "socialEnginneringDatabase/SET_SEARCH_LAST_LOAD",
        true
      );
      this.$store.commit(
        "socialEnginneringDatabase/SET_PREFIX_SEARCH_OVER",
        false
      );
      this.$store.commit(
        "socialEnginneringDatabase/SET_DATA_SEARCH_OVER",
        false
      );

      // 前缀搜索
      this.$store.dispatch("socialEnginneringDatabase/getPrefixSearchRelation");
      // 精确搜索
      this.$store.dispatch(
        "socialEnginneringDatabase/getPreciseSearchRelation"
      );
      // 内容搜索
      this.$store.dispatch("socialEnginneringDatabase/getSearchData");

      this.isShowResult = true;

      // 记录第一次搜的条件
      if (this.nowCheckPath === "/") {
        this.firstSearchObj.searchValue = this.searchValue;
        this.firstSearchObj.selectDatabaseValue = this.selectDatabaseValue;
        this.firstSearchObj.selectPathValue = this.selectPathValue;
      }

      if (!type) {
        const query = JSON.stringify({
          searchValue: this.searchValue,
          selectDatabaseValue: this.selectDatabaseValue,
          selectCaseNameValue: this.selectCaseNameValue,
          selectPathValue: this.selectPathValue,
          historicalPathArr: this.historicalPathArr,
        });
        history.pushState({ data: query }, "");
      }
    },

    // 处理popstate事件
    handlePopState(event) {
      if (this.popstateTimeout) {
        clearTimeout(this.popstateTimeout);
      }
      this.popstateTimeout = setTimeout(() => {
        if (event?.state?.data) {
          const hisStateData = JSON.parse(event.state.data);
          this.searchValue = hisStateData.searchValue;
          this.selectDatabaseValue = hisStateData.selectDatabaseValue;
          this.selectCaseNameValue = hisStateData.selectCaseNameValue;
          this.selectPathValue = [];
          this.selectPathValue.push(...hisStateData.selectPathValue);
          this.historicalPathArr = hisStateData.historicalPathArr;
          this.nowCheckPath =
            hisStateData.historicalPathArr[
              hisStateData.historicalPathArr.length - 1
            ];
          this.searchClick("router");
        }
        this.popstateTimeout = null;
      }, 300);
    },

    // 前缀搜索的滚动事件
    prefixListScroll(event) {
      if (this.prefixSeachOver) {
        return;
      }
      const element = event.target;
      const scrollTop = element.scrollTop;
      const clientHeight = element.clientHeight;
      const scrollHeight = element.scrollHeight;
      if (scrollTop + clientHeight >= scrollHeight) {
        this.$store.commit(
          "socialEnginneringDatabase/SET_SEARCH_PREFIX_LOAD",
          true
        );
        this.$store.dispatch(
          "socialEnginneringDatabase/getPrefixSearchRelation"
        );
      }
    },

    // 前缀搜索展示类型
    showTooltip(e, item) {
      if (this.tooltipTimer) {
        clearTimeout(this.tooltipTimer);
        this.tooltipTimer = null;
      }
      if (e) {
        this.setTop = e.target.offsetTop - e.target.offsetParent.scrollTop + 25;
        this.prefixTypeList = item;
      }
      this.showTooltipMode = true;
    },

    // 隐藏前缀搜索展示类型
    hideTooltip() {
      this.tooltipTimer = setTimeout(() => {
        this.showTooltipMode = false;
      }, 300);
    },

    // 内容搜索的滚动事件
    contentListScroll(event) {
      if (this.dataSearchOver) {
        return;
      }
      const element = event.target;
      const scrollTop = element.scrollTop;
      const clientHeight = element.clientHeight;
      const scrollHeight = element.scrollHeight;
      if (scrollTop + clientHeight >= scrollHeight) {
        this.$store.commit(
          "socialEnginneringDatabase/SET_SEARCH_LAST_LOAD",
          true
        );
        this.$store.dispatch("socialEnginneringDatabase/getSearchData");
      }
    },

    // 点击内容的下一次搜索
    laseDataClick(lastKey) {
      // 检查路径是否已经存在，避免重复
      if (!this.historicalPathArr.includes(lastKey)) {
        this.historicalPathArr.push(lastKey);
        this.nowCheckPath = this.dataHaveLastObj[lastKey];
        this.searchClick();
      }
    },

    // 点击精确匹配类型搜索
    preciseCardClick(key, data) {
      console.log("preciseCardClick:", key, data);
      
      this.isProgrammaticPathChange = true;
      if (this.selectPathValue.length) {
        this.selectPathValue.length = 0;
        this.selectPathValue.push(data.path);
      } else {
        this.selectPathValue = [data.path];
      }
      this.currentPathValue = [data.path];
      if (!this.historicalPathArr.includes(data.title)) {
        this.historicalPathArr.push(data.title);
        this.historicalPathArr.push(key);
        this.nowCheckPath = key;
        this.searchValue = "";
        this.searchClick();
      }
      this.showTooltipMode = false;
      this.$nextTick(() => {
        this.isProgrammaticPathChange = false;
      });
    },

    // 搜索路径点击
    historicalPathClick(path) {
      console.log("historicalPathClick:", path);
      this.nowCheckPath = path;
      const index = this.historicalPathArr.indexOf(path) + 1;
      this.historicalPathArr.splice(index);
      if (path === "/") {
        this.searchValue = this.firstSearchObj.searchValue;
        this.selectDatabaseValue = this.firstSearchObj.selectDatabaseValue;
        this.selectPathValue = [];
        this.$nextTick(() => {
          this.selectPathValue = [...this.firstSearchObj.selectPathValue];
          this.searchClick();
        });
      } else {
        this.searchClick();
      }
    },

    // 添加搜索路径
    addHistoricalPath() {
      this.showAddHisPath = true;
      this.$nextTick(() => {
        this.$refs.inputHisPathRef.focus();
      });
    },

    addHisPath() {
      if (this.addhisPathValue !== "") {
        // 检查路径是否已经存在，避免重复
        if (!this.historicalPathArr.includes(this.addhisPathValue)) {
          this.historicalPathArr.push(this.addhisPathValue);
          this.historicalPathObj[this.addhisPathValue] = {};
          this.nowCheckPath = this.addhisPathValue;
          Message.success({
            background: true,
            content: "添加搜索路径成功",
          });
        } else {
          Message.warning({
            background: true,
            content: "该路径已存在",
          });
        }
        this.showAddHisPath = false;
        this.addhisPathValue = "";
      } else {
        this.showAddHisPath = false;
      }
    },

    // 清除搜索路径
    clearHistoricalPath() {
      this.nowCheckPath = "/";
      this.historicalPathArr = ["/"];
      this.historicalPathObj = { "/": {} };
      this.selectPathValue = [];
      this.isShowResult = false;
    },

    // 搜索历史记录相关方法
    handleMouseEnter() {
      this.isMouseOver = true;
    },

    handleMouseLeave() {
      this.isMouseOver = false;
      if (!this.isFoucs) {
        this.showHisDom = false;
      }
    },

    changeShowHisDom(bool) {
      this.isFoucs = bool;
      if (!this.isMouseOver) {
        this.showHisDom = bool;
      }
    },

    setHisSearchValue() {
      if (this.searchValue) {
        if (this.hisValueData.includes(this.searchValue)) {
          const index = this.hisValueData.indexOf(this.searchValue);
          this.hisValueData.splice(index, 1);
        }
        this.hisValueData.unshift(this.searchValue);
        if (this.hisValueData.length > 10) {
          this.hisValueData.pop();
        }
        localStorage.setItem(
          window.location.host + "hisValueData",
          JSON.stringify(this.hisValueData)
        );
      }
    },

    // 处理storage事件
    handleStorage(event) {
      if (event.key === window.location.host + "hisValueData") {
        this.hisValueData =
          JSON.parse(
            localStorage.getItem(window.location.host + "hisValueData")
          ) || [];
      }
    },

    searchHisValue(item) {
      this.searchValue = item;
      this.searchClick();
    },

    clearHisSearchArr() {
      this.hisValueData = [];
      localStorage.setItem(
        window.location.host + "hisValueData",
        JSON.stringify(this.hisValueData)
      );
    },
    handleStopSearch() {
      this.isStopSearch = true;
      this.$store.commit(
        "socialEnginneringDatabase/SET_SEARCH_PREFIX_LOAD",
        false
      );
      this.$store.commit(
        "socialEnginneringDatabase/SET_SEARCH_LAST_LOAD",
        false
      );
      this.$store.commit("socialEnginneringDatabase/SET_PATH_TREE_LOAD", false);
      this.$Message.info("已停止所有搜索");
      this.$store.commit("socialEnginneringDatabase/SET_STOP_SEARCH", true);
    },
  },
};
</script>

<style scoped lang="scss">
.search_header {
  height: auto;
  width: 100%;
  margin: 10px 0;
  background-color: #fff;
  .header_text {
    font-size: 24px;
    padding: 10px 0 0 10px;
  }
}
.search_input {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  .input_btn {
    width: 500px;
    position: relative;
  }
  .search_his {
    position: absolute;
    width: 500px;
    background: #fff;
    border: 1px solid #e2e2e2;
    border-radius: 2px;
    z-index: 5;
    top: 40px;
    left: 0;
    .his_list {
      max-height: 260px;
      overflow: auto;
      .his_item {
        padding-left: 20px;
        cursor: pointer;
      }
      .his_item:hover {
        background-color: #eeecec;
      }
    }
  }
}
.his_path {
  margin: 10px 0 0 10px;
  height: 36px;
  display: flex;
  align-items: center;
  .path_list {
    margin-left: 5px;
    width: 90%;
    display: flex;
    align-items: center;
    overflow-x: auto;
    white-space: nowrap;
    .path_item {
      cursor: pointer;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .active_path {
      border: 1px solid #118d11 !important;
    }
    .path_item:hover {
      border: 1px solid #2d8cf0 !important;
    }
  }
}
.search_body {
  width: 100%;
  height: 70vh;
  overflow: auto;
  padding-left: 10px;
  display: flex;
  flex-direction: column;
  .search_criteria {
    height: 120px;
    background-color: #fff;
    width: 100%;
    .search_database {
      padding: 0px 20px;
      height: 60px;
      border-bottom: 1px dashed #adadad;
      display: flex;
      align-items: center;
      .radio_group {
        margin: 0 10px;
      }
    }
    .search_path {
      height: 60px;
      display: flex;
      align-items: center;
      width: 100%;
      .search_data_type_title {
        width: auto;
      }
      .tree_select {
        width: 70%;
      }
      .data_num {
        display: flex;
        p {
          font-weight: bold;
          margin-left: 20px;
          font-size: 15px;
          span {
            color: #2b85e4;
            padding: 0 5px;
            font-size: 19px;
          }
        }
      }
    }
  }
  .social_search_result_box {
    margin-top: 10px;
    display: flex !important;
    .social_prefix_search {
      width: 10%;
      height: 60vh;
      margin-right: 10px;
      position: relative;
      .social_prefix_title {
        padding: 2px;
      }
      .social_prefix_list {
        position: relative;
        overflow: auto;
        height: 95%;
        .social_prefix_card {
          background-color: #fff;
          margin-bottom: 5px;
          cursor: pointer;
          :deep(.ivu-card-body) {
            padding: 5px;
          }
          .social_card_text {
            display: -webkit-box;
            overflow: hidden;
            white-space: pre-wrap;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
          }
        }
        .social_prefix_card:hover {
          background-color: #cccccc;
        }
      }
      .social_no_prefix {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .social_precise_search {
      width: 15%;
      height: 60vh;
      margin-right: 10px;
      .social_precise_title {
        padding: 2px;
      }
      .social_precise_list {
        height: 95%;
        overflow: auto;
        .social_precise_card {
          margin-bottom: 5px;
          background-color: #fff;
          border-radius: 2px;
          cursor: pointer;
          .social_card_body {
            display: flex;
            align-items: center;
            padding: 5px;
            .social_precise_img {
              width: 45px;
              height: 45px;
              object-fit: cover;
              margin-right: 5px;
              max-width: 100%;
              border-radius: 4px;
            }
            .social_precise_type {
              p {
                width: 170px;
                display: -webkit-box;
                overflow: hidden;
                white-space: pre-wrap;
                -webkit-line-clamp: 3;
                line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }
          }
        }
      }
      .social_no_precise {
        width: 100%;
        height: 95%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .social_data_search {
      width: 75%;
      height: 60vh;
      .social_data_title {
        padding: 2px;
        height: 5vh;
      }
      .social_data_list {
        height: 55vh;
        overflow: auto;
        display: flex;
        flex-direction: column;
        .social_data_select_collect_box {
          display: flex;
          justify-content: space-between;
          height: 4vh;
        }
        .social_data_item {
          width: 100%;
          height: 200px !important;
          display: flex;
          flex-wrap: wrap;
          .social_item {
            overflow: auto;
            width: 45%;
            font-size: 14px;
            height: 200px;
            margin: 0 4px 4px 0;
            border: 3px solid #e9e7e7;
            background-color: #fff;
            border-radius: 5px;
            padding: 10px;
            box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: row;
            
            img {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
            }
          }
        }
        .social_search_over {
          color: #2b85e4;
          text-align: center;
          background-color: #fff;
          padding: 10px 0;
          height: 5vh;
        }
      }
      .social_no_data {
        width: 100%;
        height: 5%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    .social_search_over_tip {
      color: #2b85e4;
      text-align: center;
      background-color: #fff;
      padding: 10px 0;
      height: 5%;
    }
  }
}
:deep(.ivu-select-dropdown) {
  max-height: 300px;
}
:deep(.ivu-select-default.ivu-select-multiple .ivu-select-selection) {
  max-height: 32px;
  overflow: hidden;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
.social_prefix_type_tip {
  position: absolute;
  top: 25px;
  left: 95%;
  z-index: 10;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: #ccc;
  width: 150%;
  
  /* 添加箭头指向卡片 */
  &::before {
    content: "";
    position: absolute;
    top: 20px;
    left: -6px;
    width: 12px;
    height: 12px;
    background-color: #fff;
    transform: rotate(45deg);
    border-left: 1px solid rgba(0, 0, 0, 0.08);
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  }
  
  .social_precise_card {
    border-radius: 4px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    cursor: pointer;
    margin-bottom: 8px;
    transition: background-color 0.2s ease;
    
    &:last-child {
      margin-bottom: 0;
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f5f7fa;
    }
    
    .social_card_body {
      display: flex;
      align-items: flex-start;
      padding: 10px;
      
      .social_precise_img, 
      .precise_img {
        width: 45px;
        height: 45px;
        object-fit: cover;
        margin-right: 10px;
        max-width: 100%;
        border-radius: 4px;
      }
      
      img {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
      }
      
      .social_precise_type {
        flex: 1;
        
        p {
          width: 100%;
          margin-bottom: 4px;
          display: -webkit-box;
          overflow: hidden;
          white-space: pre-wrap;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;
          font-size: 13px;
          
          b {
            color: #333333;
          }
        }
      }
    }
  }
}
</style>
