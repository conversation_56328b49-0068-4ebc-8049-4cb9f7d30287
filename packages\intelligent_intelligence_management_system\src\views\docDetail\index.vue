<template>
  <div
    v-loading="docInfoLoading"
    class="docDetail"
  >
    <div
      v-if="gotDocInfo"
      class="main"
    >
      <div class="tabs">
        <el-tabs 
          v-model="activeTab" 
          @tab-click="tabClick"
        >
          <el-tab-pane 
            label="正文" 
            name="content_article"
          >
            <div class="contentArticle">
              {{docInfo._source.content_article}}
            </div>
          </el-tab-pane>
          <el-tab-pane 
            label="原始正文" 
            name="content"
          >
            <div class="content">
              {{docInfo._source.content}}
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div 
      v-if="gotDocInfo"
      class="aside"
    >
      <div class="info">
        <div class="key">
          文档路径: 
        </div>
        <div class="value">
          {{ docInfo._source.file_path }}
        </div>
      </div>
      <div class="info">
        <div class="key">
          文档名: 
        </div>
        <div class="value">
          {{ docInfo._source.file_name }}
        </div>
      </div>
      <div 
        v-if="docInfo._source.type=='message/rfc822'"
        class="info"
      >
        <div class="key">
          发件人: 
        </div>
        <div class="value">
          {{ docInfo._source.email_from }}
        </div>
      </div>
      <div 
        v-if="docInfo._source.type=='message/rfc822'"
        class="info"
      >
        <div class="key">
          收件人: 
        </div>
        <div class="value">
          {{ docInfo._source.email_tos }}
        </div>
      </div>
      <div class="info">
        <div class="key">
          时间: 
        </div>
        <div class="value">
          {{ docInfo._source.timestamp }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
export default {
  name: "docDetail",
  components: {
  },
  data() {
    return {
      docId: this.$route.query.id,
      docIndex: this.$route.query.index,
      docInfo: {},
      gotDocInfo: false,
      docInfoLoading: true,
      activeTab: 'content_article',
    };
  },
  created() {
  },
  mounted() {
    this.initData ()
  },
  destroyed(){
    console.log("<destroyed>");
    this.docInfo = {}
    this.gotDocInfo = false
  },
  computed: {
    ...mapState({
    }),
  },
  watch: {
    gotDocInfo: {
      handler(val, oldVal) {
        if (this.gotDocInfo == true) {
          console.log("<gotDocInfo> docInfo:", this.docInfo);
          this.docInfoLoading = false
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initData(){
      console.log("<initData> docId:", this.docId);
      console.log("<initData> docIndex:", this.docIndex);

      window.main.$main_socket.sendData(
        "Api.Search.SearchList.Query",
        [
          {
            head: {
              from: 0,
              size: 50,
            },
            msg: {
              data_range_index_name: this.docIndex,
            },
            control: {
              query_type: "public",
              add_es_query_conditions: {
                bool: {
                  must: [
                    {
                      term: {
                        _id: this.docId
                      },
                    },
                  ],
                },
              },
            },
          },
        ],
        (res) => {
          console.log("<initData> res:", res);

          if (res?.hits?.hits?.length == 1){
            this.docInfo = res.hits.hits[0]
            console.log("<initData> docInfo:", this.docInfo);
            this.gotDocInfo = true
          }else {
            alert("数据错误！")
            window.close()
          }
        }
      );
    },

    tabClick(tab, event) {
      console.log("<tabClick>", tab, event);
    }
  },
};
</script>

<style scoped lang="scss">
.docDetail {
  height: 100%;
  width:100%;
  display: flex;
  flex-direction: row;
  .main {
    height: 99%;
    width: 70%;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    margin: 0px 5px 10px 10px;
    .tabs{
      margin-left: 10px;
      width: 98%;
      .contentArticle{
        font-size: 18px;
        white-space: normal;
        word-break: break-all;
        overflow-wrap: break-word;
        overflow: auto;
      }
      .content{
        font-size: 18px;
        white-space: normal;
        word-break: break-all;
        overflow-wrap: break-word;
        overflow: auto;
      }
    }
  }
  .aside{
    height: 99%;
    width: 30%;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.2);
    border-radius: 5px;
    margin: 0px 10px 10px 5px;
    .info{
      width: 98%;
      display: flex;
      margin: 10px;
      .key{
        font-size: 18px;
        font-weight: bold;
        white-space: nowrap;
      }
      .value{
        white-space: normal;
        word-break: break-all;
        overflow-wrap: break-word;
        font-size: 18px;
        margin: 0 10px;
        color: #909399;
      }
    }
  }
}
</style>
