<template>
  <div class="main-session" style="position: relative; min-height: 200px">
    <div
      v-if="sessionShow"
      ref="scroll"
      @scroll="handleScroll"
      class="main-session-list"
      :class="{ hiddenStatusSession: hiddenStatusSession }"
    >
      <SessionList
        ref="sessionList"
        :clickSessionId="clickSessionId"
        :window-data="windowData"
        :type="SessionTypeConstant.CHAT"
        :loadingLine="loadingLine"
        @clickSessionListItem="getSessionData"
        @handleFlushList="handleFlushList"
        @handleCreateSession="handleCreateSession"
        @handleClearSession="handleClearSession"
        @handleEdit="handleEdit"
        @handleDelete="handleDelete"
      >
      </SessionList>
    </div>
    <div style="width: 15%" v-if="!sessionShow"></div>
    <div style="width: 32px" v-if="!sessionShow"></div>
    <div class="main-session-window">
      <div v-if="sessionShow">
        <div style="position: relative">
          <el-tooltip
            :content="hiddenStatusSession ? '开启侧边栏' : '隐藏侧边栏'"
            placement="right"
            popper-class="sidebar-tooltip"
          >
            <div
              class="foldable"
              :class="{ active: hiddenStatusSession }"
              @click="handleBoxClickSidebarHidden"
            >
              <img
                v-if="!hiddenStatusSession"
                :src="require('/src/assets/images/other/icon-sidebar-left.png')"
                alt=""
                class="sidebar-icon"
              />
              <img
                v-else
                :src="
                  require('/src/assets/images/other/icon-sidebar-right.png')
                "
                alt=""
              />
            </div>
          </el-tooltip>

          <el-tooltip :content="'开启新对话'" placement="right">
            <div
              style="width: 32px; position: relative; display: block"
              @click="handleCreateSession"
            >
              <i
                v-if="hiddenStatusSession"
                class="el-icon-chat-dot-round"
                style="
                  font-size: 32px;
                  color: #00b78e;
                  font-weight: 50;
                  cursor: pointer;
                "
              ></i>
            </div>
          </el-tooltip>
        </div>
      </div>
      <div style="flex-direction: column; display: flex; width: 100%; flex: 1">
        <LoadingLine :loading-line="loadingLine"></LoadingLine>

        <div
          v-if="titleName"
          style="
            text-align: center;
            font-size: 18px;
            padding-top: 10px;
            padding-bottom: 20px;
          "
        >
          <b>{{ titleName }}</b>
        </div>
        <SessionWindow
          ref="sessionWindow"
          :isTools="isTools"
          :window-data="windowData"
          :loadingLine="loadingLine"
          :responsing="responsing"
          :titleName="titleName"
          @regenerate="regenerateFn"
          @regenerateUser="regenerateUserFn"
          @hideSession="hideSessionFn"
          @handleCreateSession="handleCreateSession"
          @sendInputMessage="sendInputMessage"
          @stopRes="stopRes"
        ></SessionWindow>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import LoadingLine from "./LoadingLine";
import SessionWindow from "./window/SessionWindow";
import SessionList from "./window/SessionList";
import SessionTypeConstant from "@/common/constants/SessionType";
import { timestamp } from "@/i18n/zh/list";
import tools from "@/utils/tools";
export default {
  name: "SessionIndex",
  components: { SessionWindow, LoadingLine, SessionList },
  data() {
    return {
      nowRelation: "",
      sessionShow: true,
      titleName: "",
      loadingBox: null,
      WebSocketNum: 0,
      toolsName: {},
      getTaskListInterval: null,
      isTools: false,
      tools: [],
      showtools: [],
      responsing: false,
      SessionTypeConstant,
      loadingLine: false,
      connectId: undefined,
      sessionRecordData: [],
      hiddenStatus: false,
      contextArr: [
        /* {
          role: "system",
          content:
            "You are a article and person search server, You can call tool search_es_data to search for articles and tool query_entity_info to search for information about person .When the user prompt contains phone number, mobile number, user name,ID,email, call tool search_prefix. Generate a news analysis report and return it to me.\n Please write a briefing based on the real data found out, and do not make it up.\nIMPORTENT: Reply in Chinese!!!",
        }, */
      ],
      contextLastData: {},
      clickSessionId: "",
      myWebSocket: null,
      isManualClose: false, // 是否手动关闭标识
      reconnectTimer: null, // 重连计时器
      reconnectAttempts: 0, // 重连尝试次数（可选）,
      reSend: null,
    };
  },
  props: {
    windowData: {
      type: Object,
      required: true,
    },
  },
  watch: {
    sessionRecordData(val) {
      if (val != null) {
        this.$refs.sessionWindow.setSessionRecord(val);
      }
    },
    // 监听 loadingLine 状态变化，通知 GlobalRobot 隐藏/显示
    loadingLine(newVal) {
      console.log("IndexChat loadingLine changed:", newVal);
      this.$bus.$emit("toggleRobot", !newVal);
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.scroll.style.height = "90%";
      this.$refs.scroll.style.overflowY = "auto";
    });
  },
  created() {
    this.$nextTick(() => {
      /* this.loadingBox = this.$loading({
        lock: true,
        text: "工具配置加载中……",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
        target: document.querySelector(".main-session"),
      }); */
    });

    // 初始化时通知 GlobalRobot 显示状态
    this.$bus.$emit("toggleRobot", !this.loadingLine);

    this.getTaskListInterval = setInterval(() => {
      if (
        this.$store.state.userInfo.userinfo.hasOwnProperty("authority") &&
        window.main.$main_socket &&
        window.main.$main_socket.hasOwnProperty("instance") &&
        window.main.$main_socket.instance.readyState == 1
      ) {
        clearInterval(this.getTaskListInterval);
        this.getTaskListInterval = null;

        this.initWebSocket();
        this.sendFn();
        this.getServerTime();
      }
    }, 100);

    console.log("用户信息", window.main.$store.state.userInfo.userinfo);
  },
  computed: {
    ...mapState({
      // sessionRecordData: (state) => state.chat.sessionRecordData,
      sessionId: (state) => state.chat.sessionId,
      hiddenStatusSession: (state) => state.chat.hiddenStatusSessionList,
      toolsList: (state) => state.chat.toolsList,
      mcpCall: (state) => state.chat.mcpCall,
    }),
  },
  filters: {
    escapeHtml(text) {
      const div = document.createElement("div");
      div.textContent = text;
      return div.innerHTML;
    },
  },
  beforeDestroy() {
    // 组件销毁时自动关闭连接（非手动关闭情况）
    if (this.myWebSocket && !this.isManualClose) {
      this.myWebSocket.close();
      this.myWebSocket = null;
    }
    // 清理重连计时器
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    clearInterval(this.getTaskListInterval);

    // 组件销毁时通知 GlobalRobot 重新显示
    this.$bus.$emit("toggleRobot", true);
  },
  methods: {
    //当选中全站搜索时隐藏侧边栏
    hideSessionFn(v) {
      if (v == "all") {
        this.sessionShow = false;
      }
      if (v == "ai") {
        this.sessionShow = true;
      }
    },
    //隐藏侧边栏
    handleBoxClickSidebarHidden() {
      this.hiddenStatus = !this.hiddenStatus;
      this.$store.commit("chat/setHiddenStatusSessionList", this.hiddenStatus);
    },
    // 编辑处理
    handleEdit(item) {
      console.log("编辑处理", item);
      let arr = item.row.split(";");
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.AddData",
        [
          {
            head: {
              row_key: [item.row],
            },
            msg: {
              table: "deepseek_sessions",
              prefix: arr[arr.length - 1],
              type: "username",
              relation: "",
              data: {
                data: {
                  title: item.columnValues.d.title,
                  timestamp: parseInt(item.columnValues.d.timestamp),
                },
              },
            },
          },
        ],
        (res) => {
          console.log("AddData", res);
        }
      );
    },

    // 删除处理
    handleDelete(row) {
      console.log("handleDelete", row);
      let arr = [];
      arr.push(row);
      window.main.$main_socket.sendData(
        "Api.Search.SearchPrefixTable.DelData",
        [
          {
            head: {
              row_key: arr,
            },
            msg: {
              table: "deepseek_sessions",
              type: "username",
              relation: "",
            },
          },
        ],
        (res) => {
          console.log("res", res);
          this.sendFn();
          this.handleCreateSession();
        }
      );
    },
    //获取服务器时间
    getServerTime() {
      window.main.$main_socket.sendData(
        "Api.NodeRes.ServerTimestamp",
        [],
        (res) => {
          console.log("服务器时间2", res);
        }
      );
    },
    // 滚动事件处理
    handleScroll(e) {
      const { scrollTop, scrollHeight, clientHeight } = e.target;
      if (scrollHeight - scrollTop - clientHeight < 20) {
        console.log(2, scrollHeight - scrollTop - clientHeight);
        this.sendFn();
      }
    },
    processThinkTags(html) {
      // const thinkRegex = /<think>(.*?)<\/think>/g;
      return html.replace(/<think>/g, `<div>`);
    },
    processThinkTagsa(html) {
      // const thinkRegex = /<think>(.*?)<\/think>/g;
      return html.replace(/<\/think>/g, (match, p1) => {
        return `</div>`;
      });
    },
    //从历史记录获取对话
    async getSessionData(v) {
      try {
        let that = this;
        if (that.responsing) {
          that.$message({
            message: "请等待回复完毕",
            type: "warning",
          });
          return;
        }

        that.sessionRecordData = [];
        console.log("从历史记录获取对话", v);
        let arr = v.row.split(";");
        console.log("标题", v);
        this.titleName = v.columnValues.d.title;
        that.$store.commit("chat/handleCreateSession", arr[arr.length - 1]);
        this.initWebSocket();
        that.clickSessionId = arr[arr.length - 1];
        let relation = arr[arr.length - 1] + ";history";
        this.nowRelation = relation;
        let rowKey = [];
        let resArr = [];
        let hisArr = [];
        function sendData(rowKeyString) {
          window.main.$main_socket.sendData(
            "Api.Search.SearchPrefixTable.Query",
            [
              {
                head: {
                  size: 200,
                  row_key: rowKeyString,
                },
                msg: {
                  table: "deepseek_sessions",
                  prefix: "",
                  type: "username",
                  relation: relation, // "936;history", //state.nowCollect.id + ";" + state.collectTabsActiveName
                },
              },
            ],
            (res) => {
              if (res.length > 0) {
                console.log("res历史记录", res);

                for (let i = 0; i < res.length; i++) {
                  let myMessage = res[i].columnValues.d.message;
                  let thinkData = "";
                  if (res[i].columnValues.d.think.length > 0) {
                    thinkData =
                      "<div>" + res[i].columnValues.d.think + "</div>";
                  }

                  hisArr.push(myMessage);
                  if (myMessage.role === "user") {
                    let render = myMessage.content;
                    var htmlChar = render.replace(/&lt;/g, "<");
                    htmlChar = htmlChar.replace(/&gt;/g, ">");
                    const processedHtml = that.processThinkTags(htmlChar);
                    const processedHtmla =
                      that.processThinkTagsa(processedHtml);
                    let userData = {
                      role: "user",
                      /* content: JSON.parse(
                        JSON.stringify(
                          res[i].columnValues.d.request.messages[0].content
                        )
                      ), */
                      content: processedHtmla,
                      row: res[i].row,
                      //createTime: res[i].columnValues.d.timestamp.timestamp,
                    };
                    resArr.push(userData);
                  } else if (myMessage.role === "assistant") {
                    let render = myMessage.content;
                    let assistantData = null;
                    if (render || thinkData) {
                      /* if (render.indexOf("<think>") >= 0) {
                        var htmlChar = render.replace(/&lt;/g, "<");
                        htmlChar = htmlChar.replace(/&gt;/g, ">");
                        const processedHtml = that.processThinkTags(htmlChar);
                        const processedHtmla =
                          "<div>" + res[i].columnValues.d.think + "</div>";
                        //that.processThinkTagsa(processedHtml);
                        assistantData = {
                          role: "assistant",
                          content: processedHtmla, //res[i].columnValues.d.response.content,
                          // createTime: res[i].columnValues.d.timestamp.timestamp,
                        };
                      } else {
                        assistantData = {
                          role: "assistant",
                          content: render,
                        };
                      } */
                      assistantData = {
                        role: "assistant",
                        content: thinkData + render,
                        row: res[i].row,
                      };
                      resArr.push(assistantData);
                    }
                  } else if (myMessage.role === "tool") {
                    let render = myMessage.content;
                    let fun = res[i].columnValues.d.extra.function;
                    let toolData = {
                      row: res[i].row,
                      role: "tool",
                      content: render,
                      tool_call_id: myMessage.tool_call_id,
                      tool_calls: [
                        {
                          function: {
                            arguments: fun.arguments,
                            name: that.toolsName[fun.name],
                          },
                        },
                      ],
                    };
                    resArr.push(toolData);
                  }
                }

                sendData([res[res.length - 1].row]);
              } else {
                if (resArr.length > 0) {
                  that.sessionRecordData = resArr.reverse();
                  that.contextArr = [
                    /* {
                      role: "system",
                      content:
                        "You are a article and person search server, You can call tool search_es_data to search for articles and tool query_entity_info to search for information about person .When the user prompt contains phone number, mobile number, user name, ID,email, call tool search_prefix. Generate a news analysis report and return it to me.\n Please write a briefing based on the real data found out, and do not make it up.\nIMPORTENT: Reply in Chinese!!!",
                    }, */
                  ].concat(hisArr.reverse());
                }
              }
            }
          );
        }
        sendData(rowKey);
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
    handleFlushList() {},
    //创建新对话
    handleCreateSession() {
      this.$store.commit("chat/handleCreateSession", "");
      this.responsing = false;
      this.loadingLine = false;
      this.titleName = "";
      this.initWebSocket();
      this.clickSessionId = "";

      /* window.main.$main_socket.sendData(
        "Api.NodeRes.ServerTimestamp",
        [],
        (res) => {
          let session_id =
            (Number.MAX_SAFE_INTEGER - res.timestamp * 1000).toString() +
            Math.floor(Math.random() * 1000);
          this.$store.commit("chat/handleCreateSession", session_id);
        }
      ); */
      this.contextArr = [
        /* {
          role: "system",
          content:
            "You are a article and person search server, You can call tool search_es_data to search for articles and tool query_entity_info to search for information about person .When the user prompt contains phone number, mobile number, user name, ID,email, call tool search_prefix. Generate a news analysis report and return it to me.\n Please write a briefing based on the real data found out, and do not make it up.\nIMPORTENT: Reply in Chinese!!!",
        }, */
      ];
      this.sessionRecordData = [];
    },
    //清空数据历史记录
    handleClearSession() {
      if (confirm("确定删除该记录？")) {
        this.clickSessionId = "";
        let that = this;
        let rowArr = [];
        for (let i = 0; i < that.$store.state.chat.sessionList.length; i++) {
          rowArr.push(that.$store.state.chat.sessionList[i].row);
        }
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.DelData",
          [
            {
              head: {
                row_key: rowArr,
              },
              msg: {
                table: "deepseek_sessions",
                type: "username",
                relation: "",
              },
            },
          ],
          (res) => {
            console.log("res", res);
            this.sendFn();
            this.handleCreateSession();
          }
        );
      }
    },
    async sendFn() {
      this.$store.commit("chat/setAllDataNum", 0);
      this.$store.commit("chat/setLastRowKey", []);
      this.$store.commit("chat/setSessionList", []);
      this.$store.commit("chat/setInitSessionListGroups");
      this.$store.dispatch("chat/getAllPathDataNum");
      this.$store.dispatch("chat/sendIndicesStatus");

      try {
        const newData = await this.$store.dispatch("chat/getHistory");
      } catch (error) {
        console.error("加载数据失败:", error);
      }
    },
    //重新发送
    regenerateUserFn(v) {
      let myIndex = v.myindex;
      let that = this;
      let sendRow = [];
      let rowKey = [];
      let resArr = [];
      let hisArr = [];
      sendBefore(sendRow);
      //重新发送前获取历史记录以便于获取每一条对话的row
      function sendBefore(mySendRow) {
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.Query",

          [
            {
              head: {
                size: 200,
                row_key: mySendRow,
              },
              msg: {
                table: "deepseek_sessions",
                prefix: "",
                type: "username",
                relation: that.nowRelation, // "936;history", //state.nowCollect.id + ";" + state.collectTabsActiveName
              },
            },
          ],
          (res) => {
            if (res.length > 0) {
              for (let i = 0; i < res.length; i++) {
                let myMessage = res[i].columnValues.d.message;
                let thinkData = "";
                if (res[i].columnValues.d.think.length > 0) {
                  thinkData = "<div>" + res[i].columnValues.d.think + "</div>";
                }

                hisArr.push(myMessage);
                if (myMessage.role === "user") {
                  let render = myMessage.content;
                  var htmlChar = render.replace(/&lt;/g, "<");
                  htmlChar = htmlChar.replace(/&gt;/g, ">");
                  const processedHtml = that.processThinkTags(htmlChar);
                  const processedHtmla = that.processThinkTagsa(processedHtml);
                  let userData = {
                    role: "user",

                    content: processedHtmla,
                    row: res[i].row,
                  };
                  resArr.push(userData);
                } else if (myMessage.role === "assistant") {
                  let render = myMessage.content;
                  let assistantData = null;
                  if (render || thinkData) {
                    assistantData = {
                      role: "assistant",
                      content: thinkData + render,
                      row: res[i].row,
                    };
                    resArr.push(assistantData);
                  }
                } else if (myMessage.role === "tool") {
                  let render = myMessage.content;
                  let fun = res[i].columnValues.d.extra.function;
                  let toolData = {
                    row: res[i].row,
                    role: "tool",
                    content: render,
                    tool_call_id: myMessage.tool_call_id,
                    tool_calls: [
                      {
                        function: {
                          arguments: fun.arguments,
                          name: that.toolsName[fun.name],
                        },
                      },
                    ],
                  };
                  resArr.push(toolData);
                }
              }
              sendBefore([res[res.length - 1].row]);
            } else {
              if (resArr.length > 0) {
                that.sessionRecordData = resArr.reverse();
                that.reSend = null;
                that.reSend = findUserAndTrimArray(
                  myIndex,
                  that.sessionRecordData
                );
                that.contextArr = [].concat(hisArr.reverse());
                if (that.reSend) {
                  that.sessionRecordData = that.reSend.trimmedArray;
                  console.log("重新发送1", that.reSend, that.sessionRecordData);

                  that.sendInputMessage(that.reSend.userElement.content);
                }
              }
            }
          }
        );
      }

      let row = [];
      let rowArr = [];
      function findUserAndTrimArray(index, array) {
        // 验证参数有效性
        if (index < 0) {
          return {
            userElement: null,
            userIndex: -1,
            trimmedArray: [...array], // 返回原数组的副本
          };
        }
        let userIndex = index;

        let trimmedArray;
        let userElement = null;

        if (userIndex !== -1) {
          // 找到user元素，删除该index之后的所有元素
          userElement = array[userIndex];
          Object.assign(userElement, { content: v.mycontent });
          /*  userElement.content = v.mycontent; */
          trimmedArray = array.slice(0, userIndex + 1); // 保留到user元素（包含）
          rowArr = array.slice(userIndex);
          console.log("重新发送2", rowArr);
          for (let j = 0; j < rowArr.length; j++) {
            row.push(rowArr[j].row);
          }
          console.log("重新发送3", row);
          let relation = row[row.length - 1].split(";").slice(-3, -1).join(";");

          window.main.$main_socket.sendData(
            "Api.Search.SearchPrefixTable.DelData",
            [
              {
                head: {
                  row_key: row,
                },
                msg: {
                  table: "deepseek_sessions",
                  type: "username",
                  relation: relation,
                },
              },
            ],
            (res) => {
              console.log("res", res);
              /*   this.sendFn();
          this.handleCreateSession(); */
            }
          );
        } else {
          // 没有找到user元素，返回空数组或保持原数组
          trimmedArray = [];
        }

        return {
          userElement,
          userIndex,
          trimmedArray,
        };
      }
    },
    regenerateFn(v) {
      console.log("重新发送index", v);
      let myIndex = v;
      let that = this;
      let sendRow = [];
      let rowKey = [];
      let resArr = [];
      let hisArr = [];
      sendBefore(sendRow);
      //重新发送前获取历史记录以便于获取每一条对话的row
      function sendBefore(mySendRow) {
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.Query",

          [
            {
              head: {
                size: 200,
                row_key: mySendRow,
              },
              msg: {
                table: "deepseek_sessions",
                prefix: "",
                type: "username",
                relation: that.nowRelation, // "936;history", //state.nowCollect.id + ";" + state.collectTabsActiveName
              },
            },
          ],
          (res) => {
            if (res.length > 0) {
              for (let i = 0; i < res.length; i++) {
                let myMessage = res[i].columnValues.d.message;
                let thinkData = "";
                if (res[i].columnValues.d.think.length > 0) {
                  thinkData = "<div>" + res[i].columnValues.d.think + "</div>";
                }

                hisArr.push(myMessage);
                if (myMessage.role === "user") {
                  let render = myMessage.content;
                  var htmlChar = render.replace(/&lt;/g, "<");
                  htmlChar = htmlChar.replace(/&gt;/g, ">");
                  const processedHtml = that.processThinkTags(htmlChar);
                  const processedHtmla = that.processThinkTagsa(processedHtml);
                  let userData = {
                    role: "user",

                    content: processedHtmla,
                    row: res[i].row,
                  };
                  resArr.push(userData);
                } else if (myMessage.role === "assistant") {
                  let render = myMessage.content;
                  let assistantData = null;
                  if (render || thinkData) {
                    assistantData = {
                      role: "assistant",
                      content: thinkData + render,
                      row: res[i].row,
                    };
                    resArr.push(assistantData);
                  }
                } else if (myMessage.role === "tool") {
                  let render = myMessage.content;
                  let fun = res[i].columnValues.d.extra.function;
                  let toolData = {
                    row: res[i].row,
                    role: "tool",
                    content: render,
                    tool_call_id: myMessage.tool_call_id,
                    tool_calls: [
                      {
                        function: {
                          arguments: fun.arguments,
                          name: that.toolsName[fun.name],
                        },
                      },
                    ],
                  };
                  resArr.push(toolData);
                }
              }
              sendBefore([res[res.length - 1].row]);
            } else {
              if (resArr.length > 0) {
                that.sessionRecordData = resArr.reverse();
                that.reSend = null;
                that.reSend = findUserAndTrimArray(
                  myIndex,
                  that.sessionRecordData
                );
                that.contextArr = [].concat(hisArr.reverse());
                if (that.reSend) {
                  that.sessionRecordData = that.reSend.trimmedArray;
                  console.log("重新发送1", that.reSend, that.sessionRecordData);

                  that.sendInputMessage(that.reSend.userElement.content);
                }
              }
            }
          }
        );
      }

      let row = [];
      let rowArr = [];
      function findUserAndTrimArray(index, array) {
        console.log("findUserAndTrimArray", index);
        // 验证参数有效性
        if (index < 0) {
          return {
            userElement: null,
            userIndex: -1,
            trimmedArray: [...array], // 返回原数组的副本
          };
        }
        if (index > array.length) {
          index = array.length - 1;
        }
        // 从index-1开始向前查找最近的user元素
        let userIndex = -1;
        for (let i = index - 1; i >= 0; i--) {
          if (array[i].role === "user") {
            userIndex = i;
            break;
          }
        }

        let trimmedArray;
        let userElement = null;

        if (userIndex !== -1) {
          // 找到user元素，删除该index之后的所有元素
          userElement = array[userIndex];
          trimmedArray = array.slice(0, userIndex + 1); // 保留到user元素（包含）
          rowArr = array.slice(userIndex);
          console.log("重新发送2", rowArr);
          for (let j = 0; j < rowArr.length; j++) {
            row.push(rowArr[j].row);
          }
          console.log("重新发送3", row);
          let relation = row[row.length - 1].split(";").slice(-3, -1).join(";");

          window.main.$main_socket.sendData(
            "Api.Search.SearchPrefixTable.DelData",
            [
              {
                head: {
                  row_key: row,
                },
                msg: {
                  table: "deepseek_sessions",
                  type: "username",
                  relation: relation,
                },
              },
            ],
            (res) => {
              console.log("res", res);
              /*   this.sendFn();
          this.handleCreateSession(); */
            }
          );
        } else {
          // 没有找到user元素，返回空数组或保持原数组
          trimmedArray = [];
        }

        return {
          userElement,
          userIndex,
          trimmedArray,
        };
      }
    },
    // 发送消息
    sendInputMessage(inputMessage) {
      if (this.loadingLine) {
        this.$message.error("会话加载中......");
        return;
      }
      this.loadingLine = true;
      if (inputMessage) {
        this.socketConnectMessage(inputMessage);
      } else {
        this.$message.warning("内容不能为空");
        this.loadingLine = false;
      }
    },
    initWebSocket() {
      let that = this;

      if (that.WebSocketNum === 0) {
      }
      /* this.tools = [];
      this.showtools = []; */
      this.loadingLine = false;
      // 关闭现有连接（如果存在）
      if (this.myWebSocket) {
        console.log("关闭myWebSocket连接");
        this.myWebSocket.close();
        this.myWebSocket = null;
        console.log("that.myWebSocket", this.myWebSocket);
      }

      this.isManualClose = true;
      const setupWebsocket = (ws) => {
        // 监听事件
        ws.onopen = () => {
          console.log("WebSocket connected");
          that.WebSocketNum++;
          that.reconnectAttempts = 0; // 重置重连计数器
        };

        ws.onmessage = (event) => {
          console.log("收到WebSocket消息", event);
          try {
            let data = JSON.parse(event.data);

            console.log("eventdata", data);

            if (data.type === "error") {
              that.$message({
                message: data.content,
                type: "warning",
              });
            }
            function replaceKeyInPlace(obj, oldKey, newKey) {
              if (obj.hasOwnProperty(oldKey)) {
                obj[newKey] = obj[oldKey]; // 添加新键
                delete obj[oldKey]; // 删除旧键
              }
              return obj;
            }
            if (data.type === "tool_list") {
              try {
                let arr = [];
                if (that.WebSocketNum === 1) {
                  this.isTools = false;
                  if (
                    data.content.info.serverInfo.name ===
                      "common_tools_server" ||
                    data.content.info.serverInfo.name ===
                      "data-analysis-mcp-server"
                  ) {
                    for (let str in data.content.tools) {
                      let obj = data.content.tools[str];
                      this.showtools.push(obj);
                      this.toolsName[str] = obj.annotations.title;
                      this.tools.push({
                        function: obj.function,
                        type: obj.type,
                      });
                    }
                  }

                  this.isTools = true;
                  this.loadingBox.close();
                  //判断第几次连接  用来控制获取工具列表
                }
                console.log("this.showtools", this.showtools, this.tools);
              } catch (error) {}
            }
            console.log("that.sessionRecordData601", that.sessionRecordData);
            if (data.type === "tool_resp") {
              console.log("that.sessionRecordData600", that.sessionRecordData);
              that.sessionRecordData[
                that.sessionRecordData.length - 2
              ].content = data.content.resp;
              that.sessionRecordData[
                that.sessionRecordData.length - 2
              ].is_error = data.content.is_error;

              that.sessionRecordData[
                that.sessionRecordData.length - 2
              ].tool_calls[
                that.sessionRecordData[that.sessionRecordData.length - 2]
                  .tool_calls.length - 1
              ].function.name = this.toolsName[data.content.function.name];

              if (that.sessionRecordData.length == 2) {
                that.sessionRecordData.splice(
                  that.sessionRecordData.length - 1,
                  0,
                  {
                    role: "tool",
                    content: "",
                    tool_calls: [{ function: { arguments: "", name: "" } }],
                  }
                );
              } else {
                that.sessionRecordData.splice(
                  that.sessionRecordData.length - 1,
                  0,
                  {
                    role: "tool",
                    content: "",
                    tool_calls: [{ function: { arguments: "", name: "" } }],
                  }
                );
              }
            }
            if (data.content.choices[0].delta.content === "<think>") {
              const processedHtml = that.processThinkTags(
                data.content.choices[0].delta.content
              );
              data.content.choices[0].delta.content = processedHtml;
            }
            if (data.content.choices[0].delta.content === "</think>") {
              const processedHtml = that.processThinkTagsa(
                data.content.choices[0].delta.content
              );
              data.content.choices[0].delta.content = processedHtml;
            }
            if (data.content.choices[0].finish_reason === "stop") {
              that.responsing = false;
            } else {
              that.responsing = true;
            }
            that.loadingLine = false;

            //  let contextData = that.contextArr[that.contextArr.length - 1];

            //当前页面展示sessionRecordData内容
            let popData =
              that.sessionRecordData[that.sessionRecordData.length - 1];
            let popToolData =
              that.sessionRecordData[that.sessionRecordData.length - 2];

            if (data.type === "openai") {
              // if (data.type === "openai" && popToolData.role != "tool") {
              if (data.content.choices[0].delta.content) {
                console.log("popData", popData);
                popData.content += data.content.choices[0].delta.content;
              }

              if (data.content.choices[0].delta.tool_calls.length > 0) {
                that.sessionRecordData.push({
                  role: "tool",
                  content: "",
                  tool_calls: [
                    {
                      function: {
                        arguments:
                          data.content.choices[0].delta.tool_calls[0].function
                            .arguments,
                        name: data.content.choices[0].delta.tool_calls[0]
                          .function.name,
                      },
                    },
                  ],
                });
                that.sessionRecordData.push({
                  role: "assistant",
                  content: "",
                  tool_calls: [{ function: { arguments: "", name: "" } }],
                });
                /* if (popToolData.role != "tool") {
                  if (that.sessionRecordData.length == 2) {
                    that.sessionRecordData.splice(
                      that.sessionRecordData.length - 1,
                      0,
                      {
                        role: "tool",
                        content: "",
                        tool_calls: [{ function: { arguments: "", name: "" } }],
                      }
                    );
                  } else {
                    if (
                      that.sessionRecordData[
                        that.sessionRecordData.length - 3
                      ] != "tool"
                    ) {
                      that.sessionRecordData.splice(
                        that.sessionRecordData.length - 1,
                        0,
                        {
                          role: "tool",
                          content: "",
                          tool_calls: [
                            { function: { arguments: "", name: "" } },
                          ],
                        }
                      );
                    }
                  }
                }
                popToolData.tool_calls[
                  popToolData.tool_calls.length - 1
                ].function.arguments +=
                  data.content.choices[0].delta.tool_calls[0].function.arguments;
                if (data.content.choices[0].delta.tool_calls[0].function.name) {
                  popToolData.tool_calls[
                    popToolData.tool_calls.length - 1
                  ].function.name =
                    data.content.choices[0].delta.tool_calls[0].function.name;
                } */
              }
            }
          } catch {
            console.log("");
          }
        };

        ws.onerror = (error) => {
          console.error("WebSocket error:", error);
          that.apiErrorHandle("请求失败");
          that.handleReconnect();
        };

        ws.onclose = (event) => {
          if (!that.isManualClose) {
            console.log("Connection lost, reconnecting...");
            alert("Connection lost, reconnecting...");
            that.loadingLine = false;
            that.connectId = undefined;
            that.handleReconnect();
          }
        };
      };
      const wsUrl = new URL("wss://" + window.location.host);
      wsUrl.pathname = `/websocket/api/ai/v1`;
      if (that.sessionId.length < 1) {
        window.main.$main_socket.sendData(
          "Api.NodeRes.ServerTimestamp",
          [],
          (res) => {
            let session_id =
              (Number.MAX_SAFE_INTEGER - res.timestamp * 1000).toString() +
              Math.floor(Math.random() * 1000);
            that.$store.commit("chat/handleCreateSession", session_id);
            console.log("session_id1", session_id);
            if (!that.myWebSocket) {
              wsUrl.searchParams.append("session_id", session_id);
              console.log("wsUrl1", wsUrl);
              that.myWebSocket = new WebSocket(wsUrl.toString());
              setupWebsocket(that.myWebSocket);
            }
          }
        );
      } else {
        console.log("session_id2", that.sessionId);
        if (!that.myWebSocket) {
          wsUrl.searchParams.append("session_id", that.sessionId);
          console.log("wsUrl2", wsUrl);
          that.myWebSocket = new WebSocket(wsUrl.toString());
          setupWebsocket(that.myWebSocket);
        }
      }
    },
    //手动关闭websocket
    stopRes() {
      this.isManualClose = true;
      if (this.myWebSocket) {
        this.myWebSocket.close();
        this.myWebSocket = null;
        this.responsing = false;
        /* this.isTools = false;
        this.tools = []; */
      }
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }
      this.initWebSocket();
    },
    // 发送websocket数据
    socketConnectMessage(inputMessage) {
      const that = this;
      if (that.responsing) {
        that.$message({
          message: "请等待回复完毕",
          type: "warning",
        });
        return;
      }
      //获取服务器时间
      window.main.$main_socket.sendData(
        "Api.NodeRes.ServerTimestamp",
        [],
        (res) => {
          that.flushSendData(inputMessage); // 发送前整理数据
        }
      );
    },
    //websocket重连
    handleReconnect() {
      // 手动关闭时不重连
      if (this.isManualClose) return;

      // 限制最大重连次数（可选）
      if (this.reconnectAttempts >= 5) {
        console.log("Max reconnect attempts reached");
        return;
      }

      // 指数退避策略
      const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
      this.reconnectTimer = setTimeout(() => {
        this.reconnectAttempts++;
        this.initWebSocket();
      }, delay);
    },
    // 发送前整理数据
    flushSendData(inputMessage) {
      let that = this;
      if (this.sessionRecordData.length == 0) {
        that.nowRelation = that.sessionId + ";history";
        //给sessionList设置title和时间
        let authority = window.main.$store.state.userInfo.userinfo.authority;
        let username = window.main.$store.state.userInfo.userinfo.username;
        const rowKey =
          window.main.$tools.sha512("u;" + authority + ";" + username) +
          ";u;" +
          authority +
          ";" +
          username +
          ";" +
          this.sessionId;
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.AddData",
          [
            {
              head: {
                row_key: [rowKey],
              },
              msg: {
                table: "deepseek_sessions",
                prefix: this.sessionId,
                type: "username",
                relation: "",
                data: {
                  data: {
                    title: inputMessage,
                    timestamp: Date.now(),
                  },
                },
              },
            },
          ],
          (res) => {
            this.titleName = inputMessage;
            console.log("AddData", res);
          }
        );
      }
      if (!this.reSend) {
        let userData = {
          role: "user",
          content: JSON.parse(JSON.stringify(inputMessage)),
        };
        this.sessionRecordData.push(userData);
        this.contextArr.push(userData);
      } else {
        this.reSend = null;
      }

      //this.sessionRecordData页面展示 */
      //this.contextArr对接发送

      let assistantData = {
        role: "assistant",
        content: "",
        tool_calls: [{ function: { arguments: "", name: "" } }],
      };
      let contextArrassistantData = {
        role: "assistant",
        content: "",
        tool_calls: [{ function: { arguments: "", name: "" } }],
      };
      this.sessionRecordData.push(assistantData);
      // this.contextArr.push(contextArrassistantData);
      let hisArr = [];
      let rowKey = [];
      let dialogArr = [];
      //获取历史记录
      function sendData(rowKeyString) {
        console.log("sendData");
        window.main.$main_socket.sendData(
          "Api.Search.SearchPrefixTable.Query",
          [
            {
              head: {
                size: 200,
                row_key: rowKeyString,
              },
              msg: {
                table: "deepseek_sessions",
                prefix: "",
                type: "username",
                relation: that.sessionId + ";history", // "936;history", //state.nowCollect.id + ";" + state.collectTabsActiveName
              },
            },
          ],
          (res) => {
            console.log("Queryres");
            if (res.length > 0) {
              console.log("发送前res历史记录", res);

              for (let i = 0; i < res.length; i++) {
                let myMessage = res[i].columnValues.d.message;
                hisArr.push(myMessage);
              }

              sendData([res[res.length - 1].row]);
            } else {
              if (hisArr.length > 0) {
                console.log("发送hisArr", hisArr);
                that.contextArr = [
                  /* {
                    role: "system",
                    content:
                      "You are a article and person search server, You can call tool search_es_data to search for articles and tool query_entity_info to search for information about person .When the user prompt contains phone number, mobile number, user name, ID,email, call tool search_prefix. Generate a news analysis report and return it to me.\nPlease write a briefing based on the real data found out, and do not make it up.\n IMPORTENT: Reply in Chinese!!!",
                    //"You are a article search server, you can call search_es_data tools to search article. Generate a news analysis report and return it to me.\n IMPORTENT: Reply in Chinese!!!",
                  }, */
                ].concat(hisArr.reverse());
                let userData = {
                  role: "user",
                  content: JSON.parse(JSON.stringify(inputMessage)),
                };

                that.contextArr.push(userData);
              }
              /* dialogArr = that.contextArr; */
              dialogArr = [
                {
                  role: "assistant",
                  content: JSON.stringify(that.mcpCall),
                },
              ].concat(that.contextArr);
              let test_tools = [
                {
                  type: "function",
                  function: {
                    name: "search_prefix",
                    description:
                      "use prefix to search hbase data, return data (json string). The type of prefix can be phone number, user name, email, id number, etc\nArgs:\n\tprefix: (required) The content to be searched\n\tprefix_type: prefix parameter type used to filter the table to query. example: 'phone' | 'username' .\n",
                    parameters: {
                      properties: {
                        prefix: {
                          anyOf: [
                            {
                              type: "string",
                            },
                            {
                              type: "null",
                            },
                          ],
                          title: "Prefix",
                        },
                        prefix_type: {
                          anyOf: [
                            {
                              type: "string",
                            },
                            {
                              type: "null",
                            },
                          ],
                          default: null,
                          title: "Prefix Type",
                        },
                      },
                      required: ["prefix"],
                      title: "search_prefixArguments",
                      type: "object",
                    },
                  },
                  annotations: {
                    title: "Hbase搜索工具",
                    readOnlyHint: null,
                    destructiveHint: null,
                    idempotentHint: null,
                    openWorldHint: null,
                    description:
                      "按照加密狗证书规则搜索我公司hbase数据库内公开数据。",
                  },
                },
              ];
              /* let test_tools = [
                {
                  type: "function",
                  function: {
                    name: "search_es_data",
                    description:
                      "use query_string to search es data, return article list. query_string field do not include dates or other information. IMPORTENT: query_string in Chinese!!!\nArgs:\n\tquery_string: (required) es simple_query_string format. example: (Beijing + China) | (USA + America). Do not include dates or other information. IMPORTENT: query_string in Chinese!!!\n\tstart_timestamp: (required) query start UNIX timestamp (seconds since epoch).\n\tstop_timestamp: (required) query stop UNIX timestamp (seconds since epoch).\n\tsize: (optional) search size, default is 10.\n\tsearch_scope: (optional) list search scope, default is all.\n1. news\n2. twitter\n3. facebook\n4. linkedin\n5. telegram\n\n",
                    parameters: {
                      properties: {
                        query_string: {
                          title: "Query String",
                          type: "string",
                        },
                        start_timestamp: {
                          title: "Start Timestamp",
                          type: "integer",
                        },
                        stop_timestamp: {
                          title: "Stop Timestamp",
                          type: "integer",
                        },
                        size: {
                          anyOf: [
                            {
                              type: "integer",
                            },
                            {
                              type: "null",
                            },
                          ],
                          default: 10,
                          title: "Size",
                        },
                        search_scope: {
                          anyOf: [
                            {
                              items: {
                                type: "string",
                              },
                              type: "array",
                            },
                            {
                              type: "null",
                            },
                          ],
                          default: null,
                          title: "Search Scope",
                        },
                      },
                      required: [
                        "query_string",
                        "start_timestamp",
                        "stop_timestamp",
                      ],
                      title: "search_es_dataArguments",
                      type: "object",
                    },
                  },
                  annotations: {
                    title: "ES搜索工具",
                    readOnlyHint: null,
                    destructiveHint: null,
                    idempotentHint: null,
                    openWorldHint: null,
                    description:
                      "按照加密狗证书规则搜索我公司elasticsearch数据库内文章数据。",
                  },
                },
              ]; */
              window.main.$main_socket.sendData(
                "Api.NodeRes.ServerTimestamp",
                [],
                (resp) => {
                  const data = {
                    messages: dialogArr,
                    // model: "deepseek-r1:32b",
                    model: "qwen3:dc-max-context-14b", //"deepseek-chat", //"qwq:latest",
                    stream: true,
                    temperature: 0,
                    max_tokens: 64 * 1024,
                    msg_id:
                      (
                        Number.MAX_SAFE_INTEGER -
                        resp.timestamp * 1000
                      ).toString() + Math.floor(Math.random() * 1000),
                    system_prompt: `You are a article and person infomation search server, You can call tool search_es_data to search for articles and tool query_entity_info to search for information about person.
search_es_data need timestamp range, yow must use tools to get current time or use user query to get time range.
When the user prompt contains phone number, mobile number, user name,ID,email,company address, etc, call tool query_entity_info and tool key_person_and_organize_info_search_tool. Generate a news analysis report and return it to me.
Please write a briefing based on the real data found out, and do not make it up.
IMPORTENT: Reply in Chinese!!!`,
                    tools: that.toolsList, //that.tools,//that.tools是实时获取的mcp工具、that.toolsList是用api获取的mcp工具
                    //tools: test_tools,
                  };
                  console.log("发送eventdata", dialogArr, data);
                  that.myWebSocket.send(JSON.stringify(data) + "\n");
                  that.sendFn();
                }
              );
            }
          }
        );
      }
      sendData(rowKey);
    },
    async apiSend(connectId, inputMessage) {
      console.log("apiSend", connectId, inputMessage);
      /* if (connectId == null) {
        console.log("connectId");
        return;
      } */

      /* let that = this;
      fetch("http://localhost:11434/api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json", // 设置请求头，告诉服务器我们发送的是JSON数据
        },
        body: JSON.stringify({
          messages: [
            {
              role: "user",
              content: JSON.parse(JSON.stringify(inputMessage)),
              createTime: new Date().toLocaleString(),
            },
          ],
          model: "deepseek-r1:32b",
          stream: true,
          temperature: 1.3,
        }),
      })
        .then(async (response) => {
          // 检查响应是否有效
          if (!response.ok)
            throw new Error(`HTTP error! status: ${response.status}`);
          that.loadingLine = false;
          // 获取可读流
          const reader = response.body.getReader();
          const decoder = new TextDecoder(); // 用于解码二进制数据

          while (true) {
            const { done, value } = await reader.read(); // 读取数据块
            if (done) {
              that.loadingLine = false;
              break;
            } // 流结束

            console.log("Received chunk:", decoder.decode(value));
            let obj = JSON.parse(decoder.decode(value));
            let popData =
              that.sessionRecordData[that.sessionRecordData.length - 1];
            popData.content += obj.message.content;
            console.log("popData", that.sessionRecordData);
          }
        })
        .catch((err) => {
          console.error("Fetch error:", err);
          this.$message.error("数据返回出错了");
        }); */
    },
    apiErrorHandle(msg) {
      const that = this;
      that.$message.error(msg);
      that.sessionRecordData.pop();
      that.sessionRecordData.pop();
      that.loadingLine = false;
    },
  },
};
</script>

<style scoped lang="scss">
.main-session {
  min-height: 200px;
}

.main-session {
  height: 100%;
  width: 100%;
  display: flex;
}
.main-session-list {
  border-right: 1px solid #eee;
  width: 15%;
  min-height: 100%;
  display: flex;
  transition: width 0.2s;
  word-break: keep-all;
}
.main-session-list.hiddenStatusSession {
  width: 0;
  transition-property: all;
}

.main-session-window {
  position: relative;
  min-width: 80%;
  width: auto;

  flex: 1;
  display: flex;
  /* flex-direction: column; */
  .foldable {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    img {
      width: 32px;
      cursor: pointer;
    }
  }
}

.sidebar-icon {
  opacity: 0.2;
  transition: opacity 0.3s ease;
}

.sidebar-icon:hover {
  opacity: 1;
}
</style>
