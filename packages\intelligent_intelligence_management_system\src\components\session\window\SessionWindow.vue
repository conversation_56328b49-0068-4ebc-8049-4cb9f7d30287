<template>
  <div class="session-window">
    <div
      class="chat-session-content rounded-md"
      style="margin-bottom: 160px"
      ref="sessionWindow"
      @scroll="onScroll"
    >
      <div class="chat-content" ref="chatContent">
        <li v-for="(item, index) in sessionRecordData" :key="index">
          <window-session-content
            :titleName="titleName"
            :role="item.role"
            :content-show-type="windowData.contentShowType"
            :item-data="item"
            :index="index"
            @regenerate="regeneratefn"
            @regenerateUser="regenerateUserfn"
          ></window-session-content>
        </li>
        <div
          class="el-icon-loading"
          style="margin-left: 20%; font-size: 32px"
          v-if="responsing"
        ></div>
      </div>

      <div class="spacer" ref="spacer">
        <div
          class="createSession pointer"
          style="flex: 0 5%; border-radius: 10px; margin: 0 auto"
          v-if="sessionRecordData.length == 0 || loadingLine ? false : true"
        >
          <div @click="handleCreateSession" style="">
            <i class="el-icon-plus" style="font-size: 18px"></i>
            <span>新的对话</span>
          </div>
        </div>
      </div>
      <!--  <div style="line-height: 140px; color: #fff">k</div> -->
    </div>
    <div
      class="input-main"
      :style="{ bottom: sessionRecordData.length == 0 ? '' : '0px' }"
    >
      <div
        style="width: 850px; text-align: right"
        v-if="sessionRecordData.length === 0"
      >
        <div class="creatTask" style="margin-top: 20px">
          <el-button
            type="primary"
            @click="showAddAiDialog"
            size="mini"
            icon="el-icon-plus"
            >AI查询分析任务</el-button
          >
        </div>
      </div>

      <transition name="fade">
        <div
          v-if="sessionRecordData.length == 0 && !isDataLoading"
          class="aiStatistics"
        >
          <div class="statisticsItem" style="margin-left: -400px">
            <div class="itemTit">
              总数据量：<b>{{ (allDataNum / 100000000).toFixed(2) }}亿</b>
            </div>
            <div class="itemCon">
              <div
                class="itemConIndex"
                v-for="(item, index) in IndicesStatus"
                :key="index"
              >
                <img :src="item.src" />
                <b>{{ item.num }}</b>
              </div>
            </div>
          </div>
          <!-- <div class="statisticsItem">
            <div class="itemTit">总数据量：<b>3.5亿</b></div>
            <div class="itemCon">
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
              <div class="itemConIndex">
                <img :src="require('@/assets/images/dataImport/yuqing.png')" />
                <b>200万</b>
              </div>
            </div>
          </div> -->
        </div>
      </transition>

      <div>
        <!-- <transition name="fade">
          <div
            v-if="sessionRecordData.length == 0 && !isDataLoading"
            class="welcomeAi"
          >
            欢迎使用AI智能搜索！
          </div>
        </transition> -->
        <div style="width: 850px" v-if="sessionRecordData.length === 0">
          <div class="tabs">
            <div
              :class="[isShow === 'ai' ? 'tabs_active' : '', 'tabs_common']"
              @click="showFn('ai')"
            >
              ai搜索
            </div>
            <div class="tabs_line"></div>
            <div
              :class="[isShow === 'all' ? 'tabs_active' : '', 'tabs_common']"
              @click="showFn('all')"
            >
              全站搜索
            </div>
          </div>
        </div>

        <div v-if="isShow === 'all'">
          <div class="search" style="height: 100%">
            <!-- 舆情搜索结果组件使用示例 -->
            <!-- <newsSearchList :searchString="'美国'" :findType="'match_phrase'" :queryMode="'content_article'" :timeRange="'7天'"></newsSearchList> -->
            <div
              style="
                height: 100%;
                display: flex;
                width: 100%;
                align-items: center;
                flex-direction: column;
                justify-content: center;
              "
            >
              <div class="searchoption" style="width: 850px; margin-top: 30px">
                <div class="search_input">
                  <div class="search_input_a" style="display: flex">
                    <el-input
                      v-model="queryString"
                      placeholder="请输入关键字"
                      @keyup.enter.native="gofn"
                      ref="keyInput"
                      style="color: #333"
                    ></el-input>
                    <el-button
                      type="primary"
                      style=""
                      class="search_btn"
                      @click="gofn"
                      >搜索</el-button
                    >
                  </div>

                  <div class="advancedLay" style="top: 40px">
                    <div
                      class="searchCondition"
                      v-if="ppqueryMode === 'expression'"
                    >
                      <el-button
                        size="mini"
                        v-for="(item, index) in keyWordList"
                        :key="index"
                        @click="addSymbol(item)"
                        >{{ item }}</el-button
                      >
                    </div>

                    <el-form
                      ref="form"
                      label-width="100px"
                      style="padding-top: 30px; color: #fff !important"
                      @submit.native.prevent
                    >
                      <el-form-item label="时间范围" style="color: #fff">
                        <el-radio-group
                          v-model="timeRange"
                          style="display: block; margin-top: 10px"
                        >
                          <el-radio
                            v-for="(item, index) in timeList.filter(
                              (item) => item !== '自定义时间'
                            )"
                            :key="index"
                            :label="item"
                            >{{ item }}</el-radio
                          >
                        </el-radio-group>
                        <div
                          style="
                            display: inline-flex;
                            align-items: center;
                            margin-top: 20px;
                            height: 48px;
                          "
                        >
                          <el-radio v-model="timeRange" label="自定义时间"
                            >自定义时间</el-radio
                          >
                          <el-date-picker
                            v-if="timeRange === '自定义时间'"
                            v-model="customTime"
                            :picker-options="pickerOptions"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="timestamp"
                            @change="handleChange"
                            style="margin-left: 10px"
                          >
                          </el-date-picker>
                        </div>
                      </el-form-item>
                      <!-- <el-form-item label="查询模式">
                <el-radio-group v-model="queryMode">
                  <el-radio
                    v-for="(item, index) in rangeList"
                    :key="index"
                    :label="item.type"
                    >{{ item.text }}</el-radio
                  >
                </el-radio-group>
              </el-form-item> -->
                      <el-form-item label="匹配模式">
                        <el-radio-group v-model="ppqueryMode">
                          <el-radio
                            v-for="(item, index) in ppList"
                            :key="index"
                            :label="item.type"
                            >{{ item.text }}</el-radio
                          >
                        </el-radio-group>
                      </el-form-item>
                    </el-form>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="isShow === 'ai'" style="margin-top: 30px">
          <InputMsg
            :responsing="responsing"
            :loadingLine="loadingLine"
            :isTools="isTools"
            ref="componentInputMsg"
            @setInputMsg="setInputMsg"
            @sendInputMessage="sendInputMessage"
            @stopRes="stopRes"
          ></InputMsg>
        </div>
      </div>
      <el-dialog
        title="创建查询任务"
        :visible.sync="dialogAddAiVisible"
        width="80%"
        :close-on-click-modal="false"
        :modal-append-to-body="false"
        :append-to-body="true"
        custom-class="custom-dialog"
      >
        <el-form
          ref="addAiRuleForm"
          label-width="100px"
          style=""
          @submit.native.prevent
          :model="addAiRuleForm"
          :rules="addAiRules"
        >
          <el-form-item label="任务名称" prop="name">
            <el-input
              v-model="addAiRuleForm.name"
              @keyup.enter.native="sendSearchTask"
              size="mini"
              style="width: 400px"
            ></el-input>
          </el-form-item>

          <el-form-item label="查询字符串" prop="queryString">
            <el-input
              v-model="addAiRuleForm.queryString"
              placeholder="请输入关键字"
              ref="taskKeyInput"
              style="color: #333"
            ></el-input>
            <el-button
              size="mini"
              v-for="(item, index) in keyWordList"
              :key="index"
              @click="addTaskSymbol(item)"
              >{{ item }}</el-button
            >
          </el-form-item>

          <el-form-item label="时间范围">
            <el-radio-group
              v-model="addAiRuleForm.timeRange"
              role="radiogroup"
              aria-label="时间范围选择"
              :tabindex="0"
              style="display: block; margin-top: 15px"
            >
              <el-radio
                v-for="time in [
                  '无',
                  '今天',
                  '本月',
                  '24h',
                  '2天',
                  '3天',
                  '7天',
                  '10天',
                  '30天',
                ]"
                :key="time"
                :label="time"
                role="radio"
                :aria-checked="addAiRuleForm.timeRange === time"
                :tabindex="-1"
              >
                {{ time }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item>
            <div style="text-align: center">
              <el-button @click="resetForm('addAiRuleForm')">取 消</el-button>
              <el-button type="primary" @click="sendSearchTask"
                >确 定</el-button
              >
            </div>
          </el-form-item>
        </el-form>
        <!--  </el-form> -->
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { type } from "@/i18n/zh/list";
import InputMsg from "./inputMsg/inputMsg";
import WindowSessionContent from "@/components/session/window/chat/WindowSessionContent";
export default {
  name: "SessionWindow",
  components: {
    InputMsg,
    WindowSessionContent,
  },
  data() {
    return {
      isShow: "ai",
      isAutoScroll: true,
      inputMsg: "",
      sessionRecordData: [],
      isDataLoading: true,
      dialogAddAiVisible: false,
      addAiRuleForm: {
        name: "",

        timeRange: "无",
        queryMode: "match",
        queryString: "",
      },
      addAiRules: {
        name: [
          { required: true, message: "请输入内容", trigger: "blur" },
          /* { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" }, */
        ],
        queryString: [
          { required: true, message: "请输入字符串", trigger: "blur" },
          /* { min: 3, max: 5, message: "长度在 3 到 5 个字符", trigger: "blur" }, */
        ],
      },
      checkLists: ["username", "public", "authority"],
      queryStringActiveName: "first",
      queryStringtextarea: "",
      dynamicValidateForm: {
        domains: [
          {
            value: "",
          },
        ],
      },

      pickerOptions: {
        disabledDate(time) {
          // time 表示的是面板中每一个日期值
          // >Date.now() 只能选择今天以及今天之后的时间
          return time.getTime() > Date.now();
        },
      },

      timeList: [
        "今天",
        "24h",
        "3天",
        "7天",
        "本月",
        "30天",
        "无",
        "自定义时间",
      ],
      rangeList: [
        { type: "content_article", text: "内容查询" },
        { type: "title", text: "标题查询" },
        { type: "type", text: "媒体查询" },
        { type: "author_id", text: "作者查询" },
      ],
      ppList: [
        { type: "match_phrase", text: "精确匹配" },
        { type: "match", text: "分词匹配" },
        { type: "wildcard", text: "分词模糊匹配" },
        { type: "expression", text: "表达式匹配" },
        { type: "regexp", text: "正则匹配" },
      ],
      keyWordList: ["-", "(", ")", "+", "|"],
    };
  },
  props: {
    windowData: {
      type: Object,
      default: () => {},
    },
    isTools: { type: Boolean, default: false },
    loadingLine: { type: Boolean, default: false },
    responsing: { type: Boolean, default: false },
    titleName: { type: String, default: "" },
  },
  watch: {
    sessionRecordData: {
      handler() {
        if (this.isAutoScroll) {
          this.scrollToBottom();
        }
      },
      deep: true,
    },
    responsing: {
      handler(newVal) {
        if (newVal === false) {
          // 当响应结束时，滚动到底部
          this.scrollToBottom();
        }
      },
    },
  },
  computed: {
    ...mapState({
      IndicesStatus: (state) => state.chat.IndicesStatus,
      allDataNum: (state) => state.chat.allDataNum,
    }),
    customTime: {
      get() {
        return this.$store.state.search.conditions.conditionsData.customTime;
      },
      set(val) {
        this.$store.commit("search/conditions/setCustomTime", [
          val[0],
          val[1] + 24 * 3600000 - 1000,
        ]);
      },
    },
    timeRange: {
      get() {
        return this.$store.state.search.conditions.conditionsData.timeRange;
      },
      set(val) {
        this.$store.commit("search/conditions/setTimeRange", val);
      },
    },
    queryMode: {
      get() {
        return this.$store.state.search.conditions.conditionsData.queryMode;
      },
      set(val) {
        this.$store.commit("search/conditions/setQueryMode", val);
      },
    },
    queryString: {
      get() {
        return this.$store.state.search.conditions.conditionsData.queryString;
      },
      set(val) {
        this.$store.commit("search/conditions/setQueryString", val);
      },
    },
    ppqueryMode: {
      get() {
        return this.$store.state.search.conditions.conditionsData.ppqueryMode;
      },
      set(val) {
        this.$store.commit("search/conditions/setPPQueryMode", val);
      },
    },
  },
  created() {
    this.isDataLoading = true;
    setTimeout(() => {
      this.isDataLoading = false;
    }, 500);
  },
  methods: {
    //从windowSessionContent.vue子组件传过来的 重新发送事件
    regeneratefn(v) {
      console.log("重新发送", v);
      this.$emit("regenerate", v);
    },
    //从windowSessionContent.vue子组件传过来的 重新发送事件
    regenerateUserfn(v) {
      console.log("重新发送", v);
      this.$emit("regenerateUser", v);
    },
    showFn(v) {
      this.isShow = v;
      this.$emit("hideSession", v);
    },
    scrollToBottom() {
      this.$nextTick(() => {
        const sessionWindow = this.$refs.sessionWindow;
        if (sessionWindow) {
          sessionWindow.scrollTo({
            top: sessionWindow.scrollHeight,
            behavior: "smooth",
          });
        }
      });
    },
    setLoadingLine(val) {
      this.$emit("setLoadingLine", val);
    },
    onScroll() {
      const scrollDom = this.$refs.sessionWindow;
      const scrollTop = scrollDom.scrollTop;
      const clientHeight = scrollDom.clientHeight;
      const scrollHeight = scrollDom.scrollHeight;

      if (Math.abs(scrollTop + clientHeight - scrollHeight) <= 1) {
        this.isAutoScroll = true;
      } else {
        this.isAutoScroll = false;
      }
    },
    handleCreateSession() {
      this.$emit("handleCreateSession");
    },
    stopRes() {
      this.$emit("stopRes");
    },
    sendInputMessage() {
      this.$emit("sendInputMessage", this.inputMsg);
    },
    setInputMsg(val) {
      this.inputMsg = val;
    },
    setSessionRecord(val) {
      this.isDataLoading = true;
      setTimeout(() => {
        this.sessionRecordData = val;
        this.isAutoScroll = true;
        this.scrollToBottom();
        setTimeout(() => {
          this.isDataLoading = false;
        }, 100);
        window.main.$store.commit(
          "chat/setSessionRecordData",
          this.sessionRecordData
        );
        console.log("this.sessionRecordData");
      }, 0);
    },
    // 点击添加符号
    addTaskSymbol(symbol) {
      const input = this.$refs.taskKeyInput.$refs.input;
      const startPos = input.selectionStart;
      const endPos = input.selectionEnd;
      const currentValue = this.addAiRuleForm.queryString;

      // 在光标位置插入符号
      this.addAiRuleForm.queryString =
        currentValue.substring(0, startPos) +
        symbol +
        currentValue.substring(endPos);

      // 更新光标位置到插入符号后
      this.$nextTick(() => {
        input.setSelectionRange(
          startPos + symbol.length,
          startPos + symbol.length
        );
        input.focus();
      });
    },
    sendSearchTask() {
      this.$refs["addAiRuleForm"].validate((valid) => {
        if (!valid) {
          return false;
        }

        // 检查查询字符串是否为空
        if (
          !this.addAiRuleForm.queryString ||
          this.addAiRuleForm.queryString.length === 0 ||
          this.addAiRuleForm.queryString[0] === ""
        ) {
          this.$message.error("查询字符串不能为空");
          return false;
        }
        this.$store.dispatch("search/conditions/sendSearchTask", {
          task: this.addAiRuleForm,
        });
        this.addAiRuleForm = {
          name: "",
          timeRange: "无",
          queryMode: "match",
          queryString: "",
        };
        this.dialogAddAiVisible = false;
      });
    },
    removeDomain(item) {
      var index = this.dynamicValidateForm.domains.indexOf(item);
      if (index !== -1) {
        this.dynamicValidateForm.domains.splice(index, 1);
      }
    },
    upCHange() {
      let tmpThis = this;
      var output = document.getElementById("fileRuls");
      console.log("dddd", document.getElementById("fileRuls"));
      console.log("this.$refs.file.files[0]", this.$refs.file.files[0]);
      var reader = new FileReader();
      reader.onload = function (e) {
        // 这个事件发生，意为着数据准备好了
        // 把它复制到页面的<div>元素中

        output.innerHTML = e.target.result.replace(
          new RegExp("\n", "gm"),
          "<br />"
        );
        let arr = [];

        output.innerHTML.split("<br>").forEach((i) => {
          arr.push(i.trim());
        });
        tmpThis.task.queryString = arr;
      };
      reader.readAsText(this.$refs.file.files[0]);
    },
    addDomain() {
      this.dynamicValidateForm.domains.push({
        value: "",
        key: Date.now(),
      });
    },

    resetForm(formName) {
      this.$refs[formName].resetFields();
      this.dialogAddAiVisible = false;
    },
    showAddAiDialog() {
      this.dialogAddAiVisible = true;
    },
    handleChange() {
      this.$store.commit("search/conditions/setTimeRange", "自定义时间");
    },
    // 点击添加符号
    addSymbol(symbol) {
      const input = this.$refs.keyInput.$refs.input;
      const startPos = input.selectionStart;
      const endPos = input.selectionEnd;
      const currentValue = this.queryString;

      // 在光标位置插入符号
      this.queryString =
        currentValue.substring(0, startPos) +
        symbol +
        currentValue.substring(endPos);

      // 更新光标位置到插入符号后
      this.$nextTick(() => {
        input.setSelectionRange(
          startPos + symbol.length,
          startPos + symbol.length
        );
        input.focus();
      });
    },
    //发送搜索
    gofn() {
      this.$store.commit("search/searchList/sendSearchData");
      let tmpCondition = {};
      if (
        this.$store.state.search.conditions.conditionsData.ppqueryMode ==
        "expression"
      ) {
        tmpCondition["simple_query_string"] = {
          query: this.$store.state.search.conditions.conditionsData.queryString,
          fields: ["content"],
          default_operator: "and",
        };
      } else {
        tmpCondition[
          this.$store.state.search.conditions.conditionsData.ppqueryMode
        ] = {
          content:
            this.$store.state.search.conditions.conditionsData.queryString,
        };
      }
      window.main.$store.commit(
        "search/twLinFacSearch/setAddEsQueryConditions",
        {
          bool: {
            must: [tmpCondition],
          },
        }
      );
      window.main.$store.commit("newsSearchList/setAddEsQueryConditions", {
        bool: {
          must: [tmpCondition],
        },
      });
      window.main.$router.push({ name: "searchList" });
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs {
  margin-top: 70px;
  width: 180px;
  justify-content: space-around;
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
  .tabs_active {
    color: #4d6bfe;
    cursor: pointer;
  }
  .tabs_common {
    cursor: pointer;
  }
  .tabs_line {
    height: 28px;
    width: 1px;
    background-color: #d6d6d7;
  }
}
.session-window {
  position: relative;
  width: 100%;
  flex: 1;
  max-height: 100%;
  display: flex;
  overflow: hidden;
}

.chat-session-content {
  width: 100%;
  background-size: 100% 100%;
  box-sizing: border-box;
  overflow: auto;
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  flex: 1;
}

.chat-content {
  width: 100%;
  padding: 8px 0 14px 0;
  box-sizing: border-box;
  flex-grow: 1;
  .chat-main-content {
    width: 100%;
    display: flex;
  }

  li {
    list-style: none;
    height: auto;
    width: 1020px;
    margin: 0 auto;
    display: flex;
  }
}

.input-main {
  background-color: #fff;
  position: absolute;
  width: 100%;
  min-height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  /*  bottom: 0;
  left: 0; */
  box-sizing: border-box;
  z-index: 1510;
  .aiStatistics {
    display: flex;
    color: #000;

    .statisticsItem {
      border-radius: 4px;
      background: #f7f9fd;
      margin: 10px;
      padding: 10px;
      .itemTit {
        margin-left: 15px;
        font-size: 16px;
        font-weight: bold;
        b {
          font-size: 24px;
        }
      }
      .itemCon {
        width: 400px;
        margin-top: 10px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        .itemConIndex {
          width: 100px;
          margin-left: 15px;
          margin-right: 15px;
          display: flex;
          align-items: center;
          margin-top: 15px;
          img {
            width: 36px;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
          }
          b {
            margin-left: 10px;
          }
        }
      }
    }
  }
  .welcomeAi {
    color: #4d6bfe;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 25px;
    margin-top: 40px;
  }
}

.input-main .stop {
  width: 100%;
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px 0;

  .stop-item {
    width: 10%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--font-color-default);
    border: 1px var(--item-border-normal-color) solid;
    z-index: 1;
    transition: all 0.1s ease-out;
    background: var(--background-main);

    &:hover {
      box-shadow: 0 0 3px 1px var(--item-border-default-color);
      transition: all 0.2s ease-out;
    }
    &:active {
      transform: scale(0.92);
      transition: all 0.2s ease-in-out;
    }

    span {
      margin: 0 5px;
    }
  }
}
::v-deep .stop img {
  width: 15%;
}

.spacer {
}

::v-deep .markdown-body {
  font-size: 15px;
}
.createSession {
  width: 120px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  background-color: rgb(219, 234, 254);
  color: #4d6bfe;
  cursor: pointer;
  margin-bottom: 10px;
}
.createSession:hover {
  border: 1px dashed var(--session-list-create-session-border-hover);
  background-color: rgb(203, 224, 251);
  transform: scale(0.99);
}
.createSession > div {
  width: 100%;
  height: 32px;
  max-height: 32px;
  line-height: 42px;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border: 1px var(--session-list-create-session-border) dashed;
  transition: all 0.2s ease;
  margin-bottom: 16px;
}
.createSession span {
  font-size: 15px;
  padding: 0 2px;
}
.createSession > div:hover {
  border: 1px dashed var(--session-list-create-session-border-hover);
  transform: scale(0.99);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
<style lang="scss" scoped>
.search {
  position: relative;
  .creatTask {
    position: absolute;
    top: 20px;
    right: 20px;
  }
}

:deep(.custom-dialog) {
  position: relative;
  z-index: 3000 !important;

  // 添加表单内容的样式
  .el-form {
    .el-input__inner,
    .el-textarea__inner,
    .el-radio__label,
    .el-checkbox__label {
      // color: #8c8c8c; // 使用较浅的灰色
    }

    // 保持label字体颜色不变
    .el-form-item__label {
      color: #606266; // 默认的label颜色
    }
  }
}

/* :deep(.v-modal) {
  position: fixed !important;
  z-index: 2999 !important;
  opacity: 0.5;
  background: #000;
}
 */
:deep(.el-dialog__wrapper) {
  position: fixed !important;
  z-index: 2998 !important;
}
</style>
