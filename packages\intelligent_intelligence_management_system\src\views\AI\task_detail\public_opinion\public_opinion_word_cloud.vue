<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #faf5ff;
          line-height: 42px;
          font-weight: bold;
          color: #6b21a8;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-cloudy"></i
          ><span style="margin-left: 10px">词云</span>
        </div>
        <div style="display: flex">
          <div @click="saveDivAsImage" style="cursor: pointer">下载</div>
        </div>
      </div>
      <div>
        <div
          ref="echarts"
          id="echarts"
          style="width: 100%; height: 400px"
        ></div>
        <div
          v-if="myDataObj"
          style="padding: 10px; color: #7d7878; background-color: #eee"
        >
          {{ myDataObj }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import html2canvas from "html2canvas";

export default {
  data() {
    return {
      myDataObj: null,
    };
  },
  props: {
    activeName: {
      type: String,
      required: true,
    },
  },
  computed: {
    ...mapState({
      dataObj: function (state) {
        if (this.activeName) {
          return state.aiTaskQueue.taskDetail[this.activeName];
        }
      }, //(state) => state.aiTaskQueue.taskDetail.public_opinion,
    }),
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal) {
          for (let i = 0; i < newVal.reports.length; i++) {
            for (let str in newVal.reports[i]) {
              if (str === "word_cloud_data") {
                this.myDataObj = newVal.reports[i][str];
              }
            }
          }
          this.drawCloud(newVal.word_cloud_data);
        }
      },

      deep: true,
    },
  },
  mounted() {
    if (this.dataObj) {
      for (let i = 0; i < this.dataObj.reports.length; i++) {
        for (let str in this.dataObj.reports[i]) {
          if (str === "word_cloud_data") {
            this.myDataObj = this.dataObj.reports[i][str];
          }
        }
      }
      this.drawCloud(this.dataObj.word_cloud_data);
    }
  },
  methods: {
    drawCloud(v) {
      console.log("drawCloud", v);
      let _this = this;
      this.$nextTick(() => {
        let myChart = window.main.$echarts.init(
          document.getElementById("echarts")
        );

        // 转换函数
        function convertToEChartsFormat(dataObj) {
          return Object.keys(dataObj).map((key) => ({
            value: dataObj[key],
            name: key,
          }));
        }

        // 转换并输出
        const data = convertToEChartsFormat(v);
        console.log("词云", data);
        // // 随机颜色
        let randcolor = () => {
          let r = 100 + ~~(Math.random() * 100);
          let g = 135 + ~~(Math.random() * 100);
          let b = 100 + ~~(Math.random() * 100);
          return `rgb(${r}, ${g}, ${b})`;
        };
        myChart.setOption({
          // toolbox: {
          //   show: true,
          //   feature: {
          //     mark: {show: true},
          //     saveAsImage: {
          //       show: true,
          //       backgroundColor: "rgba(0, 0, 0, 0.75)",
          //     },
          //   },
          // },
          tooltip: {
            trigger: "item",
            padding: [10, 15],
            textStyle: {
              fontSize: 10,
            },
            formatter: (params) => {
              const { name, value } = params;
              return `
              关键词：${name} <br/>
              数量：${value}
          `;
            },
          },
          series: [
            {
              type: "wordCloud",
              //用来调整词之间的距离
              gridSize: 10,
              //用来调整字的大小范围
              // Text size range which the value in data will be mapped to.
              // Default to have minimum 12px and maximum 60px size.
              sizeRange: [14, 30],
              // Text rotation range and step in degree. Text will be rotated randomly in range [-90,                                                                             90] by rotationStep 45
              //用来调整词的旋转方向，，[0,0]--代表着没有角度，也就是词为水平方向，需要设置角度参考注释内容
              // rotationRange: [-45, 0, 45, 90],
              // rotationRange: [ 0,90],
              rotationRange: [0, 0],
              left: "center",
              top: "center",
              right: null,
              bottom: null,
              width: "200%",
              height: "200%",
              textStyle: {
                normal: {
                  color: randcolor,
                },
                emphasis: {
                  shadowBlur: 10,
                  shadowColor: "#ccc",
                  //   fontSize:50
                },
              },
              data: data,
            },
          ],
        });
        _this.$bus.$ciyunnubber = myChart;
        window.addEventListener("resize", () => {
          myChart.resize();
        });
      });
    },
    saveDivAsImage() {
      const captureElement = this.$refs.echarts;
      html2canvas(captureElement, {
        useCORS: true,
        backgroundColor: "#ffffff",
      }).then((canvas) => {
        const dataUrl = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = "词云.png";
        link.click();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.content_title {
  width: 90%;
  padding-left: 10px;
  font-size: 18px;
  color: #2440b3;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
  min-height: 20px;
}

.content_title:hover {
  text-decoration: underline;
}

/* 或直接修改圆点颜色 */
.content_title::first-letter {
}
</style>
