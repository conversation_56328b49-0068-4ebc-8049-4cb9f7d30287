<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #dcfce7;
          line-height: 42px;
          font-weight: bold;
          color: #166534;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-data-line"></i
          ><span style="margin-left: 10px">参与讨论用户数量变化</span>
        </div>
        <div style="display: flex">
          <div @click="saveDivAsImage" style="cursor: pointer">下载</div>
        </div>
      </div>
      <div
        ref="particContainer"
        id="particContainer"
        style="width: 95%; height: 250px"
      ></div>

      <div
        v-if="myDataObj"
        style="padding: 10px; color: #7d7878; background-color: #eee"
      >
        {{ myDataObj }}
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import * as echarts from "echarts";
import html2canvas from "html2canvas";

export default {
  name: "participants_user_change",
  data() {
    return {
      myDataObj: null,
    };
  },
  props: {},
  computed: {
    ...mapState({
      dataObj: (state) => state.aiTaskQueue.taskDetail.telegram,
    }),
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal) {
          this.updateChart(newVal);
          for (let i = 0; i < newVal.reports.length; i++) {
            for (let str in newVal.reports[i]) {
              if (str === "participants_user_change") {
                this.myDataObj = newVal.reports[i][str];
              }
            }
          }
        }
      },
      /* immediate: true, */
      deep: true,
    },
  },
  mounted() {
    if (this.dataObj) {
      for (let i = 0; i < this.dataObj.reports.length; i++) {
        for (let str in this.dataObj.reports[i]) {
          if (str === "participants_user_change") {
            this.myDataObj = this.dataObj.reports[i][str];
          }
        }
      }
      this.updateChart(this.dataObj);
    }
  },

  created() {},
  methods: {
    updateChart(v) {
      console.log("rawDatav", v, document.getElementById("particContainer"));
      let myChart = window.main.$echarts.init(
        document.getElementById("particContainer")
      );

      const rawData = v.participants_user_change;
      console.log("rawData", rawData);
      const xAxisData = Object.keys(rawData).sort();
      const seriesData = xAxisData.map((date) => rawData[date] || 0);
      console.log("xAxisData", xAxisData, "seriesData", seriesData);

      const option = {
        // toolbox: {
        //   show: true,
        //   feature: {
        //     mark: {show: true},
        //     saveAsImage: {show: true},
        //   },
        // },
        legend: {
          top: "top",
          left: "center",
          data: ["关键词趋势"],
        },
        tooltip: {
          trigger: "item",
          formatter: function (param) {
            const seriesName = param.seriesName;
            const xAxisValue = param.name;
            const yAxisValue = param.value;
            const colorMarker = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>`;
            return `${seriesName}<br />${colorMarker}${xAxisValue} : ${yAxisValue}`;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "8%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: true,
          data: xAxisData,
        },
        yAxis: {
          type: "value",
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
              color: "#e0e6f1",
            },
          },
        },
        series: [
          {
            name: "关键词趋势",
            type: "line",
            stack: "circle",
            symbolSize: 8,
            data: seriesData,
            areaStyle: {},
            lineStyle: {
              color: "#166534FF",
            },
            itemStyle: {
              color: "#166534FF",
            },
          },
        ],
      };
      myChart.setOption(option);
    },
    saveDivAsImage() {
      const captureElement = this.$refs.particContainer;
      html2canvas(captureElement, {
        useCORS: true,
        backgroundColor: "#ffffff",
      }).then((canvas) => {
        const dataUrl = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = "参与讨论用户数量变化.png";
        link.click();
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
