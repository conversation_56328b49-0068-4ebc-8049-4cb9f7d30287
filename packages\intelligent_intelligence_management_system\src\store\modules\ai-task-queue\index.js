import { case_id } from "@/i18n/zh/analysis";

export default {
  namespaced: true,
  state: {
    searchConditions: null, //任务的查询条件
    delPlanArr: [],
    continueToken: "",
    taskAllRow: [],
    taskTypeRadio: "all",
    taskList: [],
    pageList: [],
    taskNum: 0,
    lastRow: [],
    taskStatus: [],

    from: 0,
    size: 20,

    taskLoading: false,

    logList: [],
    logTemporaryList: [],
    logLastRowKey: [],
    logsDialogVisible: false,
    task_id: "",
    taskDetail: {},
    taskMethodDetail: null,
    intelligenceId: "",
  },
  mutations: {
    // 重置所有数据
    resetTaskState(state) {
      state.from = 0;
      state.task_status = "all";
      state.allTaskList = [];
      state.allTaskListCount = 0;
      state.taskLoading = false;
      state.parseReadyTaskList = [];
      state.parseReadyTaskListCount = 0;
      state.parseEndTaskList = [];
      state.parseEndTaskListCount = 0;
      state.parseErrorTaskList = [];
      state.parseErrorTaskListCount = 0;
      state.showTaskList = [];
    },
    setLogsDialogVisible(state, v) {
      state.logsDialogVisible = v;
    },
    // 设置任务类型,根据总数据进行过滤
    setTaskStatus(state, v) {
      window.main.$store.commit("aiTaskQueue/setPageAndData", 1);
    },
    clearTaskList(state, v) {
      state.taskAllRow = [];
      state.taskList = [];
      state.pageList = [];
      state.lastRow = [];
      state.taskNum = 0;
      state.taskTypeRadio = v;
      state.continueToken = "";
      switch (state.taskTypeRadio) {
        case "all":
          state.taskStatus = [];
          break;
        case "parse_end":
          state.taskStatus = ["parse_end"];
          break;
        case "parsing":
          state.taskStatus = ["initializing", "parsing", "job_parsing"];
          break;
        case "pause":
          state.taskStatus = ["parse_ready", "parse_pause"];
          break;
        case "error":
          state.taskStatus = ["error"];
          break;
        default:
          return;
      }
    },
    setPageAndData(state, page) {
      console.log("setPageAndData", state.taskList, page);
      state.pageList = state.taskList.slice(
        (page - 1) * 20,
        (page - 1) * 20 + 20
      );
      console.log("setPageAndData5", state.pageList);
    },
    //储存任务详情结果
    setTaskDetail(state, taskDetail) {
      console.log("setTaskDetail", taskDetail);
      state.taskDetail = taskDetail;
      if (taskDetail.hasOwnProperty("analysis_report")) {
        Object.assign(state.taskDetail, {
          bulletin: { result: taskDetail.analysis_report[0] },
        });
      }
    },
    //储存任务的查询条件
    setSearchConditions(state, v) {
      /* state.searchConditions = v; */
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Detail",
        [
          {
            head: {
              row_key: [v],
            },
            msg: {
              task_authority: "username",
              task_type: "ai_workflow_task",
            },
          },
        ],
        (res) => {
          console.log("获取任务的条件", res);
          state.searchConditions = res[0].columnValues.parm;
          if (res[0].columnValues.parm.hasOwnProperty("inputs")) {
            window.main.$store.commit(
              "search/conditions/setSearchTaskTimeRange",
              {
                time_range_end: res[0].columnValues.parm.inputs.end_timestamp,
                time_range_begin:
                  res[0].columnValues.parm.inputs.start_timestamp,
              }
            );
          }
          if (
            res[0].columnValues.parm.hasOwnProperty("add_es_query_conditions")
          ) {
            window.main.$store.commit(
              "search/conditions/setSearchTaskTimeRange",
              {
                time_range_end: res[0].columnValues.parm.end_timestamp,
                time_range_begin: res[0].columnValues.parm.start_timestamp,
              }
            );
          }
        }
      );
    },
    // 获取所有计划任务
    getAllPlanTaskList(state, v) {
      window.main.$cronjob_socket.sendData(
        "Api.CronJob.List",
        [
          {
            head: {
              size: 20,
            },
            msg: { continue: state.continueToken },
          },
        ],
        (res) => {
          if (res.items.length > 0) {
            for (let i = 0; i < res.items.length; i++) {
              state.taskAllRow.push(res.items[i].metadata.annotations.task_id);
            }
            if (res.metadata.hasOwnProperty("continue")) {
              state.continueToken = res.metadata.continue;
              window.main.$store.commit("aiTaskQueue/getAllPlanTaskList");
            } else {
              window.main.$main_socket.sendData(
                "Api.DataAnalysisTask.Detail",
                [
                  {
                    head: {
                      row_key: [...new Set(state.taskAllRow)],
                    },
                    msg: {
                      task_authority: "username",
                      task_type: "ai_workflow_task", //"intelligence_task",
                      familys: ["info", "parm"],
                      status: [],
                    },
                  },
                ],
                (res) => {
                  state.taskList = state.taskList.concat(res);
                  state.taskNum = state.taskList.length;
                  state.pageList = state.taskList.slice(0, 20);
                }
              );
            }
          }
        }
      );
    },
    // 请求总数据
    sendListAnalysisList(state) {
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.List",
        [
          {
            head: {
              size: 20,
              row_key: state.lastRow.length ? state.lastRow : [],
            },
            msg: {
              task_authority: "username",
              task_type: "ai_workflow_task", //"intelligence_task",
              familys: ["info", "parm"],
              status: state.taskStatus,
            },
          },
        ],
        "aiTaskQueue/setAllAnalysisList"
      );
    },
    // 设置总数据，当数据返回数量等于20条，继续请求
    setAllAnalysisList(state, res) {
      state.taskList = state.taskList.concat(res);
      if (res.length >= 20) {
        state.lastRow.push(res[res.length - 1].row);

        window.main.$store.commit("aiTaskQueue/sendListAnalysisList");
      } else {
        state.taskNum = state.taskList.length;
        state.pageList = state.taskList.slice(0, 20);
        console.log("<getTaskList> get all.");
        if (state.taskTypeRadio == "unfinished") return;
        if (!res.length) {
          /* window.main.$message({
            message: "暂无数据",
            type: "success",
          }); */
        } else {
          /* window.main.$message({
            message: "已获取数据",
            type: "success",
          }); */
        }
      }
    },
    //获取任务日志
    sendGetTaskLog(state, v) {
      state.task_id = v;

      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.ListLogs",
        [
          {
            head: {
              row_key: state.logLastRowKey,
              size: 200,
              //qualifier:state.logQualifier,
            },
            msg: {
              task_authority: "username",
              task_id: v,
              task_type: "ai_workflow_task",
            },
            //msg:{task_type:['social_platform_task'], familys:['logs']}
          },
        ],
        "aiTaskQueue/setGetTaskLog"
      );
    },
    setGetTaskLog(state, res) {
      function processThinkTags(html) {
        // const thinkRegex = /<think>(.*?)<\/think>/g;
        return html.replace(
          /<think>/g,
          `<div style="color:red;" class="think-block">`
        );
      }
      function processThinkTagsa(html) {
        // const thinkRegex = /<think>(.*?)<\/think>/g;
        return html.replace(/<\/think>/g, (match, p1) => {
          return `</div>`;
        });
      }

      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          let myMessage = res[i].columnValues.info.msg.message;
          let thinkData = "";
          if (res[i].columnValues.info.msg.think.length > 0) {
            thinkData = "<div>" + res[i].columnValues.info.msg.think + "</div>";
          }

          if (myMessage.role === "user") {
            let render = myMessage.content;
            var htmlChar = render.replace(/&lt;/g, "<");
            htmlChar = htmlChar.replace(/&gt;/g, ">");
            const processedHtml = processThinkTags(htmlChar);
            const processedHtmla = processThinkTagsa(processedHtml);
            let userData = {
              role: "user",
              content: processedHtmla,
            };
            state.logTemporaryList.push(userData);
          } else if (myMessage.role === "assistant") {
            let render = myMessage.content;
            let assistantData = null;
            if (render) {
              assistantData = {
                role: "assistant",
                content: thinkData + render,
              };
              state.logTemporaryList.push(assistantData);
            }
          } else if (myMessage.role === "tool") {
            let render = myMessage.content;
            let fun = res[i].columnValues.info.msg.extra.function;
            let toolData = {
              role: "tool",
              content: render,
              tool_call_id: myMessage.tool_call_id,
              tool_calls: [
                {
                  function: {
                    arguments: fun.arguments,
                    name: fun.name,
                  },
                },
              ],
            };
            state.logTemporaryList.push(toolData);
          }
        }

        let arr = [];
        arr.push(res[res.length - 1].row);
        state.logLastRowKey = arr;
        window.main.$store.commit("aiTaskQueue/sendGetTaskLog", state.task_id);
      } else {
        if (state.logTemporaryList.length > 0) {
          state.logList = state.logTemporaryList.reverse();
        }
      }

      state.logsDialogVisible = true;
      console.log("日志", state.logList);
    },
    setClearLastRowKey(state) {
      state.logLastRowKey = [];
    },
    setClearLogList(state) {
      state.logList = [];
    },
    setClearLogTemporaryList(state) {
      state.logTemporaryList = [];
    },
    // 删除任务
    sendDelTask(state, v) {
      console.log("sendDelTask");
      let continueToken = "";
      let delPlanArr = [];
      let num = 0;
      //先看任务下是否有计划任务

      let getPlanTask = function (v) {
        console.log("sendDelTask");
        window.main.$cronjob_socket.sendData(
          "Api.CronJob.List",
          [
            {
              head: {
                size: 20,
              },
              msg: {
                task_id: v[0],
                continue: continueToken,
                /*  limit: state.size,
              continue: state.continueToken, */
              },
            },
          ],
          (res) => {
            if (res.items.length > 0) {
              for (let i = 0; i < res.items.length; i++) {
                delPlanArr.push(res.items[i].metadata.labels.app);
              }
              if (res.metadata.hasOwnProperty("continue")) {
                continueToken = res.metadata.continue;
                getPlanTask(v);
              } else {
                for (let j = 0; j < delPlanArr.length; j++) {
                  window.main.$cronjob_socket.sendData(
                    "Api.CronJob.Delete",
                    [
                      {
                        head: {},
                        msg: {
                          labels: {
                            app: delPlanArr[j],
                          },
                        },
                      },
                    ],
                    (res) => {
                      num++;
                      if (num === delPlanArr.length) {
                        console.log("numnum", num);
                        window.main.$main_socket.sendData(
                          "Api.DataAnalysisTask.Del",
                          [
                            {
                              head: {
                                row_key: v,
                              },
                              msg: {
                                task_authority: "username",
                                task_type: "ai_workflow_task",
                              },
                            },
                          ],
                          "aiTaskQueue/getDel"
                        );
                      }
                    }
                  );
                }
              }
            } else {
              window.main.$main_socket.sendData(
                "Api.DataAnalysisTask.Del",
                [
                  {
                    head: {
                      row_key: v,
                    },
                    msg: {
                      task_authority: "username",
                      task_type: "ai_workflow_task",
                    },
                  },
                ],
                "aiTaskQueue/getDel"
              );
            }
          }
        );
      };
      getPlanTask(v);
    },
    getDel(state, v) {
      if (v.status === "ok") {
        window.main.$message({
          message: "删除成功",
          type: "success",
        });
        window.main.$store.commit(
          "aiTaskQueue/clearTaskList",
          state.taskTypeRadio
        );
        window.main.$store.commit("aiTaskQueue/sendListAnalysisList");
      }
    },
    //添加情报
    addIntelligence(state, data) {
      //使用当前时间戳与随机数结合当作预警词的唯一id
      function reduceNumber() {
        let soleValue = Math.round(new Date().getTime() / 1000).toString();
        let random = new Array(
          "a",
          "b",
          "c",
          "d",
          "e",
          "f",
          "g",
          "h",
          "i",
          "j",
          "k",
          "l",
          "m",
          "n"
        );
        for (let i = 0; i < 6; i++) {
          let index = Math.floor(Math.random() * 13);
          soleValue += random[index];
        }
        return soleValue;
      }
      console.log(
        "添加情报",
        data,
        window.main.$store.state.intellManageTree.caseDetail.row
      );
      state.intelligenceId = reduceNumber();
      window.main.$main_socket.sendData(
        "Api.Search.SearchList.AddOne",
        [
          {
            head: {},
            control: {
              query_type: "case",
              index: "key_intelligence",

              id: state.intelligenceId,
            },
            msg: {
              type: "case",
              case_id: window.main.$store.state.intellManageTree.caseDetail.row,
              content_article: data.content,
              title: data.name,
              params: [
                { k: "type", v: [data.type] },
                { k: "content_article", v: [data.content] },
                { k: "id", v: [state.intelligenceId] },
              ],
            },
          },
        ],
        "organization/addIntelligenceCallBack"
      );
    },
    addIntelligenceCallBack(state, data) {
      if (data.status == "ok") {
        window.main.$message.success("添加情报成功");
        /*   window.main.$store.commit("organization/getOrgani"); */
      }
    },
  },
  actions: {
    //获取任务的method
    getTaskMethod({ state, commit }, v) {
      let arr = v.split(";");
      window.main.$main_socket.sendData(
        "Api.DataAnalysisTask.Detail",
        [
          {
            head: {
              row_key: [v],
            },
            msg: {
              task_authority: "username",
              task_type: arr[1],
            },
          },
        ],
        (res) => {
          console.log("获取任务的method", res);
          state.taskMethodDetail = res[0];
        }
      );
    },
  },
};
