<template>
  <div class="searchList">
    <div class="model_lay">
      <el-row v-if="tabsActiveName != 'social_work_library'">
        <el-col :span="24">
          <div class="list_search">
            <div class="searchoption">
              <div class="search_input" style="">
                <div
                  class="search_input_item"
                  style="flex: 1; margin-right: 0px"
                >
                  <el-input
                    size="small"
                    v-model="queryString"
                    ref="keyInput"
                    placeholder="请输入检索内容"
                    @keyup.enter.native="gofn"
                  ></el-input>
                  <el-button
                    type="primary"
                    class="search_btn"
                    @click="gofn"
                    size="small"
                    >搜索</el-button
                  >
                </div>
                <div
                  class="search_input_item"
                  v-show="ppqueryMode == 'expression'"
                  style="margin-left: 20px"
                >
                  <el-button
                    size="small"
                    style="padding: 5px 10px; margin-left: 0px"
                    v-for="(item, index) in keyWordList"
                    :key="index"
                    @click="addSymbol(item)"
                    >{{ item }}</el-button
                  >
                </div>
                <div class="search_input_item" style="margin-left: 20px">
                  <div class="search_input_item_tit">时间范围</div>
                  <el-select
                    size="small"
                    :popper-class="'custompopper'"
                    v-model="timeRange"
                    placeholder="请选择"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="(item, index) in timeList"
                      :key="index"
                      :label="item"
                      :value="item"
                    >
                    </el-option>
                  </el-select>

                  <el-date-picker
                    v-show="timeRange == '自定义时间'"
                    size="small"
                    v-model="customTime"
                    :picker-options="pickerOptions"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="timestamp"
                    @change="handleChange"
                  >
                  </el-date-picker>
                </div>

                <div
                  class="search_input_item"
                  v-if="tabsActiveName === 'public_opinion'"
                >
                  <span class="search_input_item_tit">查询模式</span>
                  <el-select
                    style="width: 100px"
                    size="small"
                    v-model="queryMode"
                    placeholder="请选择"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="(item, index) in rangeList"
                      :key="index"
                      :label="item.text"
                      :value="item.type"
                    >
                    </el-option>
                  </el-select>
                </div>
                <div class="search_input_item">
                  <span class="search_input_item_tit">匹配模式</span>
                  <el-select
                    style="width: 100px"
                    size="small"
                    v-model="ppqueryMode"
                    placeholder="请选择"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="(item, index) in ppList"
                      :key="index"
                      :label="item.text"
                      :value="item.type"
                    >
                    </el-option>
                  </el-select>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row v-else>
        <el-col :span="24">
          <div class="list_search">
            <div class="searchoption">
              <div class="search_input" style="">
                <div
                  class="search_input_item"
                  style="flex: 1; margin-right: 0px"
                >
                  <el-input
                    size="small"
                    v-model="queryString"
                    placeholder="请输入检索内容"
                    @keyup.enter.native="gofn"
                  ></el-input>
                  <el-button
                    type="primary"
                    class="search_btn"
                    @click="gofn"
                    size="small"
                    >搜索</el-button
                  >
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-card class="box-card">
        <div class="box-card-con">
          <div v-if="searchTabs.length === 0" style="padding: 10px">
            <span style="color: #ccc"
              >提示：用户暂无搜索项目，请点击按钮添加项目</span
            >
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleProjectLink"
              >项目</el-button
            >
          </div>

          <el-tabs
            v-model="tabsActiveName"
            @tab-click="handleClickTabs"
            style="width: 100%; padding: 0px 10px"
            class="no-underline-tabs"
          >
            <el-tab-pane
              :label="item.label"
              :name="item.value"
              v-for="item in searchTabs"
              :key="item.value"
            >
            </el-tab-pane>
          </el-tabs>
        </div>
        <div class="listTableLay" v-if="searchTabs.length != 0">
          <component
            :is="tabsActiveName"
            v-if="showComponent"
            :key="componentKey"
            ref="searchCom"
          ></component>
        </div>
      </el-card>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import telegram from "@/layout/components/telegram-search-components/telegram-search-index.vue";
import twitter from "@/layout/components/searchListComponent/twitter.vue";
import public_opinion from "@/layout/components/searchListComponent/publicOpinion.vue";
import social_work_library from "@/layout/components/social-enginnering-database/social-enginnering-database.vue";
import linkedin from "@/layout/components/searchListComponent/linkedin.vue";
import facebook from "@/layout/components/searchListComponent/facebook-search-index.vue";
import document from "@/layout/components/searchListComponent/document.vue";
export default {
  components: {
    telegram,
    twitter,
    public_opinion,
    social_work_library,
    linkedin,
    facebook,
    document,
  },
  data() {
    return {
      componentKey: 0,
      pickerOptions: {
        disabledDate(time) {
          // time 表示的是面板中每一个日期值
          // 只能选择今天以及今天之后的时间
          return time.getTime() > Date.now();
        },
      },
      ppList: [
        { type: "match_phrase", text: "精确匹配" },
        { type: "match", text: "分词匹配" },
        { type: "wildcard", text: "分词模糊匹配" },
        { type: "expression", text: "表达式匹配" },
        { type: "regexp", text: "正则匹配" },
      ],
      timeList: [
        "今天",
        "24h",
        "3天",
        "7天",
        "本月",
        "30天",
        "无",
        "自定义时间",
      ],
      rangeList: [
        { type: "content_article", text: "内容查询" },
        /*  { type: "title", text: "标题查询" }, */
        { type: "type", text: "媒体查询" },
        { type: "author_id", text: "作者查询" },
      ],

      // esdata
      keyWordList: ["-", "(", ")", "+", "|"],
    };
  },
  watch: {
    "$store.state.projectManage.dataList": {
      handler(newVal, oldVal) {
        // 当 projectManage.dataList 发生变化时，重新获取 tabs
        this.$store.dispatch("search/searchList/getTabs");
      },
      deep: true, // 深度监听，确保能监听到数组内部的变化
    },
  },
  computed: {
    ...mapState({
      showComponent: (state) => state.search.searchList.showComponent,
    }),
    tabsActiveName: {
      get() {
        return this.$store.state.search.searchList.tabsActiveName;
      },
      set(val) {
        this.$store.commit("search/searchList/setTabsActiveName", val);
      },
    },
    searchTabs: {
      get() {
        return this.$store.state.search.searchList.searchTabs;
      },
      set(val) {},
    },
    customTime: {
      get() {
        return this.$store.state.search.conditions.conditionsData.customTime;
      },
      set(val) {
        this.$store.commit("search/conditions/setCustomTime", [
          val[0],
          val[1] + 24 * 3600000 - 1000,
        ]);
      },
    },
    timeRange: {
      get() {
        return this.$store.state.search.conditions.conditionsData.timeRange;
      },
      set(val) {
        this.$store.commit("search/conditions/setTimeRange", val);
      },
    },
    queryMode: {
      get() {
        return this.$store.state.search.conditions.conditionsData.queryMode;
      },
      set(val) {
        this.$store.commit("search/conditions/setQueryMode", val);
      },
    },
    queryString: {
      get() {
        return this.$store.state.search.conditions.conditionsData.queryString;
      },
      set(val) {
        this.$store.commit("search/conditions/setQueryString", val);
      },
    },
    ppqueryMode: {
      get() {
        return this.$store.state.search.conditions.conditionsData.ppqueryMode;
      },
      set(val) {
        this.$store.commit("search/conditions/setPPQueryMode", val);
      },
    },
  },
  created() {
    //发送获取tabs的请求，将数据保存在store中
    this.$store.dispatch("search/searchList/getTabs");
    this.createdSearchTab();
  },
  methods: {
    //项目管理
    handleProjectLink() {
      this.$store.commit("userInfo/setprojectDialogVisible", true);
    },
    newTime() {
      //写一个五秒后发送请求然后处理返回值循环展示
      //写一个定时器
    },
    reload() {
      this.componentKey += 1; // 修改 key 触发子组件重建‌:ml-citation{ref="1,6" data="citationList"}
    },
    handleCommand(command) {
      console.log("index", command);
      //this.activeTab = command;  // 通过下拉选项直接切换标签‌:ml-citation{ref="1,5" data="citationList"}
    },
    //点击tabs
    handleClickTabs(v) {
      console.log("vvvv", this.tabsActiveName);
      // if (
      //   this.tabsActiveName === "social_work_library" &&
      //   this.$refs.searchCom
      // ) {
      //   // 检查组件是否存在且有searchClick方法
      //   if (typeof this.$refs.searchCom.initialize === "function") {
      //     this.$refs.searchCom.initialize();
      //   } else {
      //     console.warn("社工库组件中未找到searchClick方法");
      //   }
      // }
    },
    //zookeeper路径的增删改查
    createdSearchTab() {
      /* window.main.$constant_socket.sendData(
        "Api.Node.CreateNode",
        [
          {
            msg: {
              nodePath:
                "/etc/web/intelligent_intelligence_management_system/search_list_tabs",
              data: {
                tabs: [
                  {
                    isShow: true,
                    value: "public_opinion",
                    label: "舆情",
                    index: 0,
                  },
                  {
                    isShow: true,
                    value: "telegram",
                    label: "Telegram",
                    index: 1,
                  },
                  {
                    isShow: true,
                    value: "twitter",
                    label: "Twitter",
                    index: 2,
                  },
                  {
                    isShow: true,
                    value: "facebook",
                    label: "Facebook",
                    index: 3,
                  },
                  {
                    isShow: true,
                    value: "linkedin",
                    label: "Linkedin",
                    index: 4,
                  },
                  {
                    isShow: true,
                    value: "document",
                    label: "文档",
                    index: 5,
                  },
                  {
                    isShow: true,
                    value: "social_work_library",
                    label: "社工库",
                    index: 6,
                  },
                ],
              },
            },
          },
        ],
        (res) => {
          console.log("rescreatedSearchTabs", res);
        }
      ); */
      /* window.main.$constant_socket.sendData(
        "Api.Node.DeleteNode",
        [
          {
            msg: {
              nodePath: "/etc/web/intelligent_intelligence_management_system",
            },
          },
        ],
        (res) => {
          console.log("rescreatedSearchTab", res);
        }
      ); */
      /* window.main.$constant_socket.sendData(
        "Api.Node.NodeData",
        [
          {
            msg: {
              "/etc/web/intelligent_intelligence_management_system/search_list_tabs":
                "",
            },
          },
        ],
        (res) => {
          console.log("rescreatedSearchTab", res);
        }
      ); */
    },
    // 选择日历自定义时间
    handleChange() {
      this.$store.commit("search/conditions/setTimeRange", "自定义时间");
    },

    // 点击添加符号
    addSymbol(symbol) {
      const input = this.$refs.keyInput.$refs.input;
      const startPos = input.selectionStart;
      const endPos = input.selectionEnd;
      const currentValue = this.queryString;

      // 在光标位置插入符号
      this.queryString =
        currentValue.substring(0, startPos) +
        symbol +
        currentValue.substring(endPos);

      // 更新光标位置到插入符号后
      this.$nextTick(() => {
        input.setSelectionRange(
          startPos + symbol.length,
          startPos + symbol.length
        );
        input.focus();
      });
    },
    //发送搜索
    gofn() {
      let tmpCondition = {};
      if (
        this.$store.state.search.conditions.conditionsData.ppqueryMode ==
        "expression"
      ) {
        tmpCondition["simple_query_string"] = {
          query: this.$store.state.search.conditions.conditionsData.queryString,
          fields: ["content"],
          default_operator: "and",
        };
      } else {
        tmpCondition[
          this.$store.state.search.conditions.conditionsData.ppqueryMode
        ] = {
          content:
            this.$store.state.search.conditions.conditionsData.queryString,
        };
      }
      window.main.$store.commit(
        "search/twLinFacSearch/setAddEsQueryConditions",
        {
          bool: {
            must: [tmpCondition],
          },
        }
      );
      window.main.$store.commit("newsSearchList/setAddEsQueryConditions", {
        bool: {
          must: [tmpCondition],
        },
      });
      this.$store.commit("search/searchList/sendSearchData");
      // this.reload();
      // 添加延时确保组件已经重新渲染
      if (
        this.tabsActiveName === "social_work_library" &&
        this.$refs.searchCom
      ) {
        // 检查组件是否存在且有searchClick方法
        if (typeof this.$refs.searchCom.initialize === "function") {
          this.$refs.searchCom.initialize();
        } else {
          console.warn("社工库组件中未找到searchClick方法");
        }
      } else if (this.tabsActiveName === "document"){
        console.log("<gofn> 文档点击搜索");
        this.$refs.searchCom.clearDoc()
        this.$refs.searchCom.search()
      }else {
        this.reload();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.model_lay {
  width: 80%;
  margin: 0 auto;
  overflow: auto;
  height: 100%;
  color: #000;
  display: flex;
  flex-direction: column;

  .searchoption {
    background-color: #fff;
  }

  .box-card {
    margin-top: 12px;
    flex: 1;
    display: flex;
    flex-direction: column;

    ::v-deep.el-card__body {
      height: 100%;
    }

    .listTableLay {
      height: 100%;
    }
  }
}

.search_input {
  padding-left: 10px;
  height: 48px;
  align-items: center;
  margin-top: 10px;
  width: 100%;
  display: flex;
}

/* 控制下拉框宽度 */
.custompopper {
  width: 400px !important;
  /* 固定下拉框宽度 */
  min-width: 200px !important;
  /* 最小宽度保护 */
}

/* 下拉框内选项项宽度 */
.custompopper .el-select-dropdown__item {
  width: 100%;
  /* 占满下拉框宽度 */
  padding: 0 20px;
  /* 调整选项内边距 */
}

.search_input_item {
  margin-right: 10px;
  align-items: center;
  display: flex;

  .search_input_item_tit {
    margin-right: 10px;
    font-size: 14px;
    color: #333333;
  }
}

.searchList {
  height: 100%;
}
</style>
