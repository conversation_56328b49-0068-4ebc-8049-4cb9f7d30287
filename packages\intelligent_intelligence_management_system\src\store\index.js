import Vue from "vue";
import Vuex from "vuex";
import layout from "./modules/layout";
import userInfo from "./modules/userInfo";
import search from "./modules/search.js";
import deployment from "./modules/deployment.js";
import telegramSearch from "./modules/telegram-search/telegram-search-index";
import socialEnginneringDatabase from "./modules/social-enginnering-database/social-enginnering-database";
import mailbox from "./modules/mailbox";
import home from "./modules/home";
import aiAnalyze from "./modules/ai-analyze";
import aiPlanTask from "./modules/ai-plan-task";
import chat from "./modules/chat";
import aiTaskQueue from "./modules/ai-task-queue";
import warningManagement from "./modules/warning-management";
import relationsshipTopologyDiagram from "./modules/archives/relationsship-topology-diagram";
import organizationTopologyMap from "./modules/archives/organization-topology-map";
import newsSearchList from "./modules/newsSearchList";
import collect from "./modules/collect";
import intellManageTree from "./modules/caseTree/intellManageTree";
import NewsDisplay from "./modules/NewsDisplay";

import personOrganizationDialog from "./modules/archives/person-organization-dialog";
import person from "./modules/archives/person";
import organization from "./modules/archives/organization";
import projectManage from "./modules/projectManage";
import detailIntelligence from "./modules/archives/detail-intelligence";
import channelNum from "./modules/archives/channelNum";
import relevantPerson from "./modules/archives/relevantPerson";
import relevantOriganization from "./modules/archives/relevantOriganization";
Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    socket: {
      isConnected: false,
    },
    navactive: "",
    break: "",
    timer: null,
    spidertimer: null,
    searchRangeTimer: null,
    allFlag: false,
    authorityFlag: false,
    checkSessionIdSocketOnOpen: false,
    restrictedPopover: 1,
  },
  mutations: {
    setAuthorityFlag(state, v) {
      state.authorityFlag = v;
    },
    setAllFlag(state, v) {
      state.allFlag = v;
    },
    setSpiderTimer(state, v) {
      state.spidertimer = v;
    },
    setSearchRangeTimer(state, v) {
      state.searchRangeTimer = v;
    },
    setTimer(state, v) {
      state.timer = v;
    },
    socket_on_open(state, event) {
      state.socket.isConnected = true;
      state.restrictedPopover += 1;
      if (state.restrictedPopover === 7) {
        window.main.$notify({
          type: "success",
          message: "通信连接已建立 (Socket connected)",
          duration: 500,
        });
      }
    },

    socket_on_close(state, event) {
      state.socket.isConnected = false;
      for (let i = 0; i < state.socket.socketList.length; i++) {
        if (event.wsName === state.socket.socketList[i].wsName) {
          state.socket.socketList[i].isConnected = false;
          break;
        }
      }
      this.commit("showMessage", {
        wsName: event.wsName,
        message: event.wsName + "网络不稳定 (websocket closed)",
      });
    },
    setBreak(state, v) {
      state.break = v;
    },

    socket_on_error({ commit }, event) {
      // console.error('Error')
      window.main.$message.error("网络不稳定 (websocket closed)");
    },
    socket_on_message({ commit }, message) {
      if (!message.hasOwnProperty("error")) {
        return;
      }
      if (message.error == null || message.error == "") {
        return;
      }
      if (message.error == "no authority") {
        if (!window.main.$store.state.authorityFlag) {
          this.commit("setAuthorityFlag", true);
          window.main.$confirm("登录失效，请重新登录！(no authority)");
          window.main.$router.push("/");
          console.log("登录失效，请重新登录！");
          return;
        }
      } else {
        // window.main.$confirm(message.error)
        window.main.$message.error(message.error);
      }
      return;
    },

    socket_reconnect({ commit }, count) {
      // window.main.$message.error("网络不稳定，重连第" + count + "次")
    },

    socket_reconnect_error({ commit }) {
      // console.error('Socket disconnected')
      window.main.$message.error("网络不稳定 (websocket disconnected)");
    },
    activefn(state, v) {
      state.navactive = v;
    },
  },
  modules: {
    layout,
    userInfo,
    search,
    telegramSearch,
    newsSearchList,
    collect,
    NewsDisplay,
    intellManageTree,
    socialEnginneringDatabase,
    deployment,
    person,
    mailbox,
    home,
    aiAnalyze,
    aiPlanTask,
    chat,
    aiTaskQueue,
    organization,
    warningManagement,
    projectManage,
    relationsshipTopologyDiagram,
    organizationTopologyMap,
    personOrganizationDialog,
    detailIntelligence,
    channelNum,
    relevantPerson,
    relevantOriganization,
  },
});
