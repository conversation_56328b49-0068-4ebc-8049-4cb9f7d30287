<template>
  <div>
    <el-card class="box-card">
      <div
        style="
          height: 42px;
          background-color: #dcfce7;
          line-height: 42px;
          font-weight: bold;
          color: #166534;
          display: flex;
          justify-content: space-between;
          padding: 0 10px;
        "
      >
        <div>
          <i class="el-icon-data-line"></i
          ><span style="margin-left: 10px">作者发文统计</span>
        </div>
        <div style="display: flex">
          <div @click="saveDivAsImage" style="cursor: pointer">下载</div>
        </div>
      </div>
      <div
        ref="userMessage"
        id="userMessage"
        style="width: 95%; height: 250px"
      ></div>

      <div
        v-if="myDataObj"
        style="padding: 10px; color: #7d7878; background-color: #eee"
      >
        {{ myDataObj }}
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapState } from "vuex";
import * as echarts from "echarts";
import html2canvas from "html2canvas";

export default {
  name: "column_chart_data",
  data() {
    return {
      myDataObj: null,
    };
  },
  props: {
    activeName: {
      type: String,
      required: true,
    },
  },
  computed: {
    ...mapState({
      dataObj: function (state) {
        if (this.activeName) {
          return state.aiTaskQueue.taskDetail[this.activeName];
        }
      }, //(state) => state.aiTaskQueue.taskDetail.twitter,
    }),
  },
  watch: {
    dataObj: {
      handler(newVal) {
        if (newVal) {
          this.updateChart(newVal);
          for (let i = 0; i < newVal.reports.length; i++) {
            for (let str in newVal.reports[i]) {
              if (str === "column_chart_data") {
                this.myDataObj = newVal.reports[i][str];
              }
            }
          }
        }
      },
      /* immediate: true, */
      deep: true,
    },
  },
  mounted() {
    if (this.dataObj) {
      console.log("this.dataObj", this.dataObj);
      for (let i = 0; i < this.dataObj.reports.length; i++) {
        for (let str in this.dataObj.reports[i]) {
          if (str === "column_chart_data") {
            this.myDataObj = this.dataObj.reports[i][str];
          }
        }
      }
      this.updateChart(this.dataObj);
    }
  },

  created() {},
  methods: {
    updateChart(v) {
      console.log("rawDatav", v, document.getElementById("userMessage"));
      let myChart = window.main.$echarts.init(
        document.getElementById("userMessage")
      );

      const rawData = v.column_chart_data;
      console.log("rawData", rawData);
      const xAxisData = Object.keys(rawData).sort();
      const seriesData = xAxisData.map((date) => rawData[date] || 0);
      console.log("xAxisDatasss", xAxisData, "seriesData", seriesData);

      const option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            data: xAxisData,
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: "Direct",
            type: "bar",
            barWidth: "60%",
            data: seriesData,
          },
        ],
      };
      myChart.setOption(option);
    },
    saveDivAsImage() {
      const captureElement = this.$refs.userMessage;
      html2canvas(captureElement, {
        useCORS: true,
        backgroundColor: "#ffffff",
      }).then((canvas) => {
        const dataUrl = canvas.toDataURL("image/png");
        const link = document.createElement("a");
        link.href = dataUrl;
        link.download = "作者发文统计.png";
        link.click();
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
